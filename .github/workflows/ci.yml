name: CI

on:
  push:
    branches:
      - main
      - "v[0-9]+.[0-9]+.x"
    tags:
      - "v*"

  pull_request:
    branches:
      - "**"

concurrency:
  # Allow only one workflow per any non-`main` branch.
  group: ${{ github.workflow }}-${{ github.ref_name }}-${{ github.ref_name == 'main' && github.sha || 'anysha' }}
  cancel-in-progress: true

env:
  CARGO_TERM_COLOR: always
  CARGO_INCREMENTAL: 0
  RUST_BACKTRACE: 1

jobs:
  job_spec:
    name: Decide which jobs to run
    if: github.repository_owner == 'zed-industries'
    outputs:
      run_tests: ${{ steps.filter.outputs.run_tests }}
      run_license: ${{ steps.filter.outputs.run_license }}
    runs-on:
      - ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          # 350 is arbitrary; ~10days of history on main (5secs); full history is ~25secs
          fetch-depth: ${{ github.ref == 'refs/heads/main' && 2 || 350 }}
      - name: Fetch git history and generate output filters
        id: filter
        run: |
          if [ -z "$GITHUB_BASE_REF" ]; then
            echo "Not in a PR context (i.e., push to main/stable/preview)"
            COMPARE_REV=$(git rev-parse HEAD~1)
          else
            echo "In a PR context comparing to pull_request.base.ref"
            git fetch origin "$GITHUB_BASE_REF" --depth=350
            COMPARE_REV=$(git merge-base "origin/${GITHUB_BASE_REF}" HEAD)
          fi
          # Specify anything which should skip full CI in this regex:
          # - docs/
          # - .github/ISSUE_TEMPLATE/
          # - .github/workflows/  (except .github/workflows/ci.yml)
          SKIP_REGEX='^(docs/|\.github/(ISSUE_TEMPLATE|workflows/(?!ci)))'
          if [[ $(git diff --name-only $COMPARE_REV ${{ github.sha }} | grep -vP "$SKIP_REGEX") ]]; then
            echo "run_tests=true" >> $GITHUB_OUTPUT
          else
            echo "run_tests=false" >> $GITHUB_OUTPUT
          fi
          if [[ $(git diff --name-only $COMPARE_REV ${{ github.sha }} | grep '^Cargo.lock') ]]; then
            echo "run_license=true" >> $GITHUB_OUTPUT
          else
            echo "run_license=false" >> $GITHUB_OUTPUT
          fi

  migration_checks:
    name: Check Postgres and Protobuf migrations, mergability
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    timeout-minutes: 60
    runs-on:
      - self-hosted
      - macOS
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false
          fetch-depth: 0 # fetch full history

      - name: Remove untracked files
        run: git clean -df

      - name: Find modified migrations
        shell: bash -euxo pipefail {0}
        run: |
          export SQUAWK_GITHUB_TOKEN=${{ github.token }}
          . ./script/squawk

      - name: Ensure fresh merge
        shell: bash -euxo pipefail {0}
        run: |
          if [ -z "$GITHUB_BASE_REF" ];
          then
            echo "BUF_BASE_BRANCH=$(git merge-base origin/main HEAD)" >> $GITHUB_ENV
          else
            git checkout -B temp
            git merge -q origin/$GITHUB_BASE_REF -m "merge main into temp"
            echo "BUF_BASE_BRANCH=$GITHUB_BASE_REF" >> $GITHUB_ENV
          fi

      - uses: bufbuild/buf-setup-action@v1
        with:
          version: v1.29.0
      - uses: bufbuild/buf-breaking-action@v1
        with:
          input: "crates/proto/proto/"
          against: "https://github.com/${GITHUB_REPOSITORY}.git#branch=${BUF_BASE_BRANCH},subdir=crates/proto/proto/"

  workspace_hack:
    timeout-minutes: 60
    name: Check workspace-hack crate
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - buildjet-8vcpu-ubuntu-2204
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - name: Add Rust to the PATH
        run: echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Install cargo-hakari
        uses: clechasseur/rs-cargo@8435b10f6e71c2e3d4d3b7573003a8ce4bfc6386 # v2
        with:
          command: install
          args: cargo-hakari@0.9.35

      - name: Check workspace-hack Cargo.toml is up-to-date
        run: |
          cargo hakari generate --diff || {
            echo "To fix, run script/update-workspace-hack or script/update-workspace-hack.ps1";
            false
          }
      - name: Check all crates depend on workspace-hack
        run: |
          cargo hakari manage-deps --dry-run || {
            echo "To fix, run script/update-workspace-hack or script/update-workspace-hack.ps1"
            false
          }

  style:
    timeout-minutes: 60
    name: Check formatting and spelling
    needs: [job_spec]
    if: github.repository_owner == 'zed-industries'
    runs-on:
      - buildjet-8vcpu-ubuntu-2204
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - uses: pnpm/action-setup@fe02b34f77f8bc703788d5817da081398fad5dd2 # v4.0.0
        with:
          version: 9

      - name: Prettier Check on /docs
        working-directory: ./docs
        run: |
          pnpm dlx prettier@${PRETTIER_VERSION} . --check || {
            echo "To fix, run from the root of the Zed repo:"
            echo "  cd docs && pnpm dlx prettier@${PRETTIER_VERSION} . --write && cd .."
            false
          }
        env:
          PRETTIER_VERSION: 3.5.0

      - name: Prettier Check on default.json
        run: |
          pnpm dlx prettier@${PRETTIER_VERSION} assets/settings/default.json --check || {
            echo "To fix, run from the root of the Zed repo:"
            echo "  pnpm dlx prettier@${PRETTIER_VERSION} assets/settings/default.json --write"
            false
          }
        env:
          PRETTIER_VERSION: 3.5.0

      # To support writing comments that they will certainly be revisited.
      - name: Check for todo! and FIXME comments
        run: script/check-todos

      - name: Check modifier use in keymaps
        run: script/check-keymaps

      - name: Run style checks
        uses: ./.github/actions/check_style

      - name: Check for typos
        uses: crate-ci/typos@8e6a4285bcbde632c5d79900a7779746e8b7ea3f # v1.24.6
        with:
          config: ./typos.toml

  check_docs:
    timeout-minutes: 60
    name: Check docs
    needs: [job_spec]
    if: github.repository_owner == 'zed-industries'
    runs-on:
      - buildjet-8vcpu-ubuntu-2204
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: Build docs
        uses: ./.github/actions/build_docs

  macos_tests:
    timeout-minutes: 60
    name: (macOS) Run Clippy and tests
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - self-hosted
      - macOS
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: cargo clippy
        run: ./script/clippy

      - name: Install cargo-machete
        uses: clechasseur/rs-cargo@8435b10f6e71c2e3d4d3b7573003a8ce4bfc6386 # v2
        with:
          command: install
          args: cargo-machete@0.7.0

      - name: Check unused dependencies
        uses: clechasseur/rs-cargo@8435b10f6e71c2e3d4d3b7573003a8ce4bfc6386 # v2
        with:
          command: machete

      - name: Check licenses
        run: |
          script/check-licenses
          if [[ "${{ needs.job_spec.outputs.run_license }}" == "true" ]]; then
            script/generate-licenses /tmp/zed_licenses_output
          fi

      - name: Check for new vulnerable dependencies
        if: github.event_name == 'pull_request'
        uses: actions/dependency-review-action@67d4f4bd7a9b17a0db54d2a7519187c65e339de8 # v4
        with:
          license-check: false

      - name: Run tests
        uses: ./.github/actions/run_tests

      - name: Build collab
        run: cargo build -p collab

      - name: Build other binaries and features
        run: |
          cargo build --workspace --bins --all-features
          cargo check -p gpui --features "macos-blade"
          cargo check -p workspace
          cargo build -p remote_server
          cargo check -p gpui --examples

      # Since the macOS runners are stateful, so we need to remove the config file to prevent potential bug.
      - name: Clean CI config file
        if: always()
        run: rm -rf ./../.cargo

  linux_tests:
    timeout-minutes: 60
    name: (Linux) Run Clippy and tests
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - buildjet-16vcpu-ubuntu-2204
    steps:
      - name: Add Rust to the PATH
        run: echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Cache dependencies
        uses: swatinem/rust-cache@9d47c6ad4b02e050fd481d890b2ea34778fd09d6 # v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          cache-provider: "buildjet"

      - name: Install Linux dependencies
        run: ./script/linux

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: cargo clippy
        run: ./script/clippy

      - name: Run tests
        uses: ./.github/actions/run_tests

      - name: Build other binaries and features
        run: |
          cargo build -p zed
          cargo check -p workspace
          cargo check -p gpui --examples

      # Even the Linux runner is not stateful, in theory there is no need to do this cleanup.
      # But, to avoid potential issues in the future if we choose to use a stateful Linux runner and forget to add code
      # to clean up the config file, I’ve included the cleanup code here as a precaution.
      # While it’s not strictly necessary at this moment, I believe it’s better to err on the side of caution.
      - name: Clean CI config file
        if: always()
        run: rm -rf ./../.cargo

  build_remote_server:
    timeout-minutes: 60
    name: (Linux) Build Remote Server
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - buildjet-8vcpu-ubuntu-2204
    steps:
      - name: Add Rust to the PATH
        run: echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Cache dependencies
        uses: swatinem/rust-cache@9d47c6ad4b02e050fd481d890b2ea34778fd09d6 # v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          cache-provider: "buildjet"

      - name: Install Clang & Mold
        run: ./script/remote-server && ./script/install-mold 2.34.0

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: Build Remote Server
        run: cargo build -p remote_server

      - name: Clean CI config file
        if: always()
        run: rm -rf ./../.cargo

  windows_clippy:
    timeout-minutes: 60
    name: (Windows) Run Clippy
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on: windows-2025-16
    steps:
      # more info here:- https://github.com/rust-lang/cargo/issues/13020
      - name: Enable longer pathnames for git
        run: git config --system core.longpaths true

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Create Dev Drive using ReFS
        run: ./script/setup-dev-driver.ps1

      # actions/checkout does not let us clone into anywhere outside ${{ github.workspace }}, so we have to copy the clone...
      - name: Copy Git Repo to Dev Drive
        run: |
          Copy-Item -Path "${{ github.workspace }}" -Destination "${{ env.ZED_WORKSPACE }}" -Recurse

      - name: Cache dependencies
        uses: swatinem/rust-cache@9d47c6ad4b02e050fd481d890b2ea34778fd09d6 # v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          workspaces: ${{ env.ZED_WORKSPACE }}
          cache-provider: "github"

      - name: Configure CI
        run: |
          mkdir -p ${{ env.CARGO_HOME }} -ErrorAction Ignore
          cp ./.cargo/ci-config.toml ${{ env.CARGO_HOME }}/config.toml

      - name: cargo clippy
        working-directory: ${{ env.ZED_WORKSPACE }}
        run: ./script/clippy.ps1

      - name: Check dev drive space
        working-directory: ${{ env.ZED_WORKSPACE }}
        # `setup-dev-driver.ps1` creates a 100GB drive, with CI taking up ~45GB of the drive.
        run: ./script/exit-ci-if-dev-drive-is-full.ps1 95

      # Since the Windows runners are stateful, so we need to remove the config file to prevent potential bug.
      - name: Clean CI config file
        if: always()
        run: |
          if (Test-Path "${{ env.CARGO_HOME }}/config.toml") {
            Remove-Item -Path "${{ env.CARGO_HOME }}/config.toml"  -Force
          }

  # Windows CI takes twice as long as our other platforms and fast github hosted runners are expensive.
  # But we still want to do CI, so let's only run tests on main and come back to this when we're
  # ready to self host our Windows CI (e.g. during the push for full Windows support)
  windows_tests:
    timeout-minutes: 60
    name: (Windows) Run Tests
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    # Use bigger runners for PRs (speed); smaller for async (cost)
    runs-on: ${{ github.event_name == 'pull_request' && 'windows-2025-32' || 'windows-2025-16' }}
    steps:
      # more info here:- https://github.com/rust-lang/cargo/issues/13020
      - name: Enable longer pathnames for git
        run: git config --system core.longpaths true

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Create Dev Drive using ReFS
        run: ./script/setup-dev-driver.ps1

      # actions/checkout does not let us clone into anywhere outside ${{ github.workspace }}, so we have to copy the clone...
      - name: Copy Git Repo to Dev Drive
        run: |
          Copy-Item -Path "${{ github.workspace }}" -Destination "${{ env.ZED_WORKSPACE }}" -Recurse

      - name: Cache dependencies
        uses: swatinem/rust-cache@9d47c6ad4b02e050fd481d890b2ea34778fd09d6 # v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          workspaces: ${{ env.ZED_WORKSPACE }}
          cache-provider: "github"

      - name: Configure CI
        run: |
          mkdir -p ${{ env.CARGO_HOME }} -ErrorAction Ignore
          cp ./.cargo/ci-config.toml ${{ env.CARGO_HOME }}/config.toml

      - name: Run tests
        uses: ./.github/actions/run_tests_windows
        with:
          working-directory: ${{ env.ZED_WORKSPACE }}

      - name: Build Zed
        working-directory: ${{ env.ZED_WORKSPACE }}
        run: cargo build

      - name: Check dev drive space
        working-directory: ${{ env.ZED_WORKSPACE }}
        # `setup-dev-driver.ps1` creates a 100GB drive, with CI taking up ~45GB of the drive.
        run: ./script/exit-ci-if-dev-drive-is-full.ps1 95

      # Since the Windows runners are stateful, so we need to remove the config file to prevent potential bug.
      - name: Clean CI config file
        if: always()
        run: |
          if (Test-Path "${{ env.CARGO_HOME }}/config.toml") {
            Remove-Item -Path "${{ env.CARGO_HOME }}/config.toml"  -Force
          }

  tests_pass:
    name: Tests Pass
    runs-on: ubuntu-latest
    needs:
      - job_spec
      - style
      - migration_checks
      # run_tests: If adding required tests, add them here and to script below.
      - workspace_hack
      - linux_tests
      - build_remote_server
      - macos_tests
      - windows_clippy
      - windows_tests
    if: |
      github.repository_owner == 'zed-industries' &&
      always()
    steps:
      - name: Check all tests passed
        run: |
          # Check dependent jobs...
          RET_CODE=0
          # Always check style
          [[ "${{ needs.style.result }}" != 'success' ]] && { RET_CODE=1; echo "style tests failed"; }

          # Only check test jobs if they were supposed to run
          if [[ "${{ needs.job_spec.outputs.run_tests }}" == "true" ]]; then
            [[ "${{ needs.workspace_hack.result }}"       != 'success' ]] && { RET_CODE=1; echo "Workspace Hack failed"; }
            [[ "${{ needs.macos_tests.result }}"          != 'success' ]] && { RET_CODE=1; echo "macOS tests failed"; }
            [[ "${{ needs.linux_tests.result }}"          != 'success' ]] && { RET_CODE=1; echo "Linux tests failed"; }
            [[ "${{ needs.windows_tests.result }}"        != 'success' ]] && { RET_CODE=1; echo "Windows tests failed"; }
            [[ "${{ needs.windows_clippy.result }}"       != 'success' ]] && { RET_CODE=1; echo "Windows clippy failed"; }
            [[ "${{ needs.build_remote_server.result }}"  != 'success' ]] && { RET_CODE=1; echo "Remote server build failed"; }
            # This check is intentionally disabled. See: https://github.com/zed-industries/zed/pull/28431
            # [[ "${{ needs.migration_checks.result }}"     != 'success' ]] && { RET_CODE=1; echo "Migration Checks failed"; }
          fi
          if [[ "$RET_CODE" -eq 0 ]]; then
            echo "All tests passed successfully!"
          fi
          exit $RET_CODE

  bundle-mac:
    timeout-minutes: 120
    name: Create a macOS bundle
    runs-on:
      - self-hosted
      - bundle
    if: |
      startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling')
    needs: [macos_tests]
    env:
      MACOS_CERTIFICATE: ${{ secrets.MACOS_CERTIFICATE }}
      MACOS_CERTIFICATE_PASSWORD: ${{ secrets.MACOS_CERTIFICATE_PASSWORD }}
      APPLE_NOTARIZATION_KEY: ${{ secrets.APPLE_NOTARIZATION_KEY }}
      APPLE_NOTARIZATION_KEY_ID: ${{ secrets.APPLE_NOTARIZATION_KEY_ID }}
      APPLE_NOTARIZATION_ISSUER_ID: ${{ secrets.APPLE_NOTARIZATION_ISSUER_ID }}
      ZED_CLIENT_CHECKSUM_SEED: ${{ secrets.ZED_CLIENT_CHECKSUM_SEED }}
      DIGITALOCEAN_SPACES_ACCESS_KEY: ${{ secrets.DIGITALOCEAN_SPACES_ACCESS_KEY }}
      DIGITALOCEAN_SPACES_SECRET_KEY: ${{ secrets.DIGITALOCEAN_SPACES_SECRET_KEY }}
    steps:
      - name: Install Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
        with:
          node-version: "18"

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          # We need to fetch more than one commit so that `script/draft-release-notes`
          # is able to diff between the current and previous tag.
          #
          # 25 was chosen arbitrarily.
          fetch-depth: 25
          clean: false
          ref: ${{ github.ref }}

      - name: Limit target directory size
        run: script/clear-target-dir-if-larger-than 100

      - name: Determine version and release channel
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        run: |
          # This exports RELEASE_CHANNEL into env (GITHUB_ENV)
          script/determine-release-channel

      - name: Draft release notes
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        run: |
          mkdir -p target/
          # Ignore any errors that occur while drafting release notes to not fail the build.
          script/draft-release-notes "$RELEASE_VERSION" "$RELEASE_CHANNEL" > target/release-notes.md || true
          script/create-draft-release target/release-notes.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create macOS app bundle
        run: script/bundle-mac

      - name: Rename binaries
        if: ${{ github.ref == 'refs/heads/main' }} || contains(github.event.pull_request.labels.*.name, 'run-bundling') }}
        run: |
          mv target/aarch64-apple-darwin/release/Zed.dmg target/aarch64-apple-darwin/release/Zed-aarch64.dmg
          mv target/x86_64-apple-darwin/release/Zed.dmg target/x86_64-apple-darwin/release/Zed-x86_64.dmg

      - name: Upload app bundle (aarch64) to workflow run if main branch or specific label
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: ${{ github.ref == 'refs/heads/main' }} || contains(github.event.pull_request.labels.*.name, 'run-bundling') }}
        with:
          name: Zed_${{ github.event.pull_request.head.sha || github.sha }}-aarch64.dmg
          path: target/aarch64-apple-darwin/release/Zed-aarch64.dmg

      - name: Upload app bundle (x86_64) to workflow run if main branch or specific label
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: ${{ github.ref == 'refs/heads/main' }} || contains(github.event.pull_request.labels.*.name, 'run-bundling') }}
        with:
          name: Zed_${{ github.event.pull_request.head.sha || github.sha }}-x86_64.dmg
          path: target/x86_64-apple-darwin/release/Zed-x86_64.dmg

      - uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        name: Upload app bundle to release
        if: ${{ env.RELEASE_CHANNEL == 'preview' || env.RELEASE_CHANNEL == 'stable' }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            target/zed-remote-server-macos-x86_64.gz
            target/zed-remote-server-macos-aarch64.gz
            target/aarch64-apple-darwin/release/Zed-aarch64.dmg
            target/x86_64-apple-darwin/release/Zed-x86_64.dmg
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  bundle-linux-x86_x64:
    timeout-minutes: 60
    name: Linux x86_x64 release bundle
    runs-on:
      - buildjet-16vcpu-ubuntu-2004 # ubuntu 20.04 for minimal glibc
    if: |
      startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling')
    needs: [linux_tests]
    env:
      ZED_CLIENT_CHECKSUM_SEED: ${{ secrets.ZED_CLIENT_CHECKSUM_SEED }}
      DIGITALOCEAN_SPACES_ACCESS_KEY: ${{ secrets.DIGITALOCEAN_SPACES_ACCESS_KEY }}
      DIGITALOCEAN_SPACES_SECRET_KEY: ${{ secrets.DIGITALOCEAN_SPACES_SECRET_KEY }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Install Linux dependencies
        run: ./script/linux && ./script/install-mold 2.34.0

      - name: Determine version and release channel
        if: startsWith(github.ref, 'refs/tags/v')
        run: |
          # This exports RELEASE_CHANNEL into env (GITHUB_ENV)
          script/determine-release-channel

      - name: Create Linux .tar.gz bundle
        run: script/bundle-linux

      - name: Upload Artifact to Workflow - zed (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-${{ github.event.pull_request.head.sha || github.sha }}-x86_64-unknown-linux-gnu.tar.gz
          path: target/release/zed-*.tar.gz

      - name: Upload Artifact to Workflow - zed-remote-server (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-remote-server-${{ github.event.pull_request.head.sha || github.sha }}-x86_64-unknown-linux-gnu.gz
          path: target/zed-remote-server-linux-x86_64.gz

      - name: Upload Artifacts to release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'run-bundling')) }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            target/zed-remote-server-linux-x86_64.gz
            target/release/zed-linux-x86_64.tar.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  bundle-linux-aarch64: # this runs on ubuntu22.04
    timeout-minutes: 60
    name: Linux arm64 release bundle
    runs-on:
      - buildjet-16vcpu-ubuntu-2204-arm
    if: |
      startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling')
    needs: [linux_tests]
    env:
      ZED_CLIENT_CHECKSUM_SEED: ${{ secrets.ZED_CLIENT_CHECKSUM_SEED }}
      DIGITALOCEAN_SPACES_ACCESS_KEY: ${{ secrets.DIGITALOCEAN_SPACES_ACCESS_KEY }}
      DIGITALOCEAN_SPACES_SECRET_KEY: ${{ secrets.DIGITALOCEAN_SPACES_SECRET_KEY }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Install Linux dependencies
        run: ./script/linux

      - name: Determine version and release channel
        if: startsWith(github.ref, 'refs/tags/v')
        run: |
          # This exports RELEASE_CHANNEL into env (GITHUB_ENV)
          script/determine-release-channel

      - name: Create and upload Linux .tar.gz bundles
        run: script/bundle-linux

      - name: Upload Artifact to Workflow - zed (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-${{ github.event.pull_request.head.sha || github.sha }}-aarch64-unknown-linux-gnu.tar.gz
          path: target/release/zed-*.tar.gz

      - name: Upload Artifact to Workflow - zed-remote-server (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-remote-server-${{ github.event.pull_request.head.sha || github.sha }}-aarch64-unknown-linux-gnu.gz
          path: target/zed-remote-server-linux-aarch64.gz

      - name: Upload Artifacts to release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'run-bundling')) }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            target/zed-remote-server-linux-aarch64.gz
            target/release/zed-linux-aarch64.tar.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  freebsd:
    timeout-minutes: 60
    runs-on: github-8vcpu-ubuntu-2404
    if: |
      startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling')
    needs: [linux_tests]
    name: Build Zed on FreeBSD
    # env:
    #   MYTOKEN : ${{ secrets.MYTOKEN }}
    #   MYTOKEN2: "value2"
    steps:
      - uses: actions/checkout@v4
      - name: Build FreeBSD remote-server
        id: freebsd-build
        uses: vmactions/freebsd-vm@c3ae29a132c8ef1924775414107a97cac042aad5 # v1.2.0
        with:
          # envs: "MYTOKEN MYTOKEN2"
          usesh: true
          release: 13.5
          copyback: true
          prepare: |
            pkg install -y \
              bash curl jq git \
              rustup-init cmake-core llvm-devel-lite pkgconf protobuf # ibx11 alsa-lib rust-bindgen-cli
          run: |
            freebsd-version
            sysctl hw.model
            sysctl hw.ncpu
            sysctl hw.physmem
            sysctl hw.usermem
            git config --global --add safe.directory /home/<USER>/work/zed/zed
            rustup-init --profile minimal --default-toolchain none -y
            . "$HOME/.cargo/env"
            ./script/bundle-freebsd
            mkdir -p out/
            mv "target/zed-remote-server-freebsd-x86_64.gz" out/
            rm -rf target/
            cargo clean

      - name: Upload Artifact to Workflow - zed-remote-server (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-remote-server-${{ github.event.pull_request.head.sha || github.sha }}-x86_64-unknown-freebsd.gz
          path: out/zed-remote-server-freebsd-x86_64.gz

      - name: Upload Artifacts to release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'run-bundling')) }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            out/zed-remote-server-freebsd-x86_64.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  nix-build:
    name: Build with Nix
    uses: ./.github/workflows/nix.yml
    if: github.repository_owner == 'zed-industries' && contains(github.event.pull_request.labels.*.name, 'run-nix')
    secrets: inherit
    with:
      flake-output: debug
      # excludes the final package to only cache dependencies
      cachix-filter: "-zed-editor-[0-9.]*-nightly"

  auto-release-preview:
    name: Auto release preview
    if: |
      startsWith(github.ref, 'refs/tags/v')
      && endsWith(github.ref, '-pre') && !endsWith(github.ref, '.0-pre')
    needs: [bundle-mac, bundle-linux-x86_x64, bundle-linux-aarch64, freebsd]
    runs-on:
      - self-hosted
      - bundle
    steps:
      - name: gh release
        run: gh release edit $GITHUB_REF_NAME --draft=false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
