# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 4

[[package]]
name = "activity_indicator"
version = "0.1.0"
dependencies = [
 "anyhow",
 "auto_update",
 "editor",
 "extension_host",
 "futures 0.3.31",
 "gpui",
 "language",
 "project",
 "release_channel",
 "smallvec",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "addr2line"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfbe277e56a376000877090da837660b4427aad530e3028d44e0bffe4f89a1c1"
dependencies = [
 "gimli",
]

[[package]]
name = "adler2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "512761e0bb2578dd7380c6baaa0f4ce03e84f95e960231d1dec8bf4d7d6e2627"

[[package]]
name = "aes"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b169f7a6d4742236a0a00c541b845991d0ac43e546831af1249753ab4c3aa3a0"
dependencies = [
 "cfg-if",
 "cipher",
 "cpufeatures",
 "zeroize",
]

[[package]]
name = "agent"
version = "0.1.0"
dependencies = [
 "agent_settings",
 "anyhow",
 "assistant_context_editor",
 "assistant_slash_command",
 "assistant_slash_commands",
 "assistant_tool",
 "assistant_tools",
 "audio",
 "buffer_diff",
 "chrono",
 "client",
 "collections",
 "component",
 "context_server",
 "convert_case 0.8.0",
 "db",
 "editor",
 "extension",
 "feature_flags",
 "file_icons",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "git",
 "gpui",
 "heed",
 "html_to_markdown",
 "http_client",
 "indexed_docs",
 "indoc",
 "inventory",
 "itertools 0.14.0",
 "jsonschema",
 "language",
 "language_model",
 "log",
 "lsp",
 "markdown",
 "menu",
 "multi_buffer",
 "notifications",
 "ordered-float 2.10.1",
 "parking_lot",
 "paths",
 "picker",
 "postage",
 "pretty_assertions",
 "project",
 "prompt_store",
 "proto",
 "rand 0.8.5",
 "ref-cast",
 "release_channel",
 "rope",
 "rules_library",
 "schemars",
 "search",
 "serde",
 "serde_json",
 "serde_json_lenient",
 "settings",
 "smol",
 "sqlez",
 "streaming_diff",
 "telemetry",
 "telemetry_events",
 "terminal",
 "terminal_view",
 "text",
 "theme",
 "thiserror 2.0.12",
 "time",
 "time_format",
 "ui",
 "ui_input",
 "urlencoding",
 "util",
 "uuid",
 "watch",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zed_llm_client",
 "zstd",
]

[[package]]
name = "agent_settings"
version = "0.1.0"
dependencies = [
 "anthropic",
 "anyhow",
 "collections",
 "deepseek",
 "fs",
 "gpui",
 "language_model",
 "lmstudio",
 "log",
 "mistral",
 "ollama",
 "open_ai",
 "paths",
 "schemars",
 "serde",
 "serde_json",
 "serde_json_lenient",
 "settings",
 "workspace-hack",
 "zed_llm_client",
]

[[package]]
name = "ahash"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "891477e0c6a8957309ee5c45a6368af3ae14bb510732d2684ffa19af310920f9"
dependencies = [
 "getrandom 0.2.15",
 "once_cell",
 "version_check",
]

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if",
 "const-random",
 "getrandom 0.2.15",
 "once_cell",
 "serde",
 "version_check",
 "zerocopy 0.7.35",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "alacritty_terminal"
version = "0.25.1-dev"
source = "git+https://github.com/zed-industries/alacritty.git?branch=add-hush-login-flag#828457c9ff1f7ea0a0469337cc8a37ee3a1b0590"
dependencies = [
 "base64 0.22.1",
 "bitflags 2.9.0",
 "home",
 "libc",
 "log",
 "miow",
 "parking_lot",
 "piper",
 "polling",
 "regex-automata 0.4.9",
 "rustix-openpty",
 "serde",
 "signal-hook",
 "unicode-width 0.1.14",
 "vte",
 "windows-sys 0.59.0",
]

[[package]]
name = "aliasable"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "250f629c0161ad8107cf89319e990051fae62832fd343083bea452d93e2205fd"

[[package]]
name = "aligned-vec"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4aa90d7ce82d4be67b64039a3d588d38dbcc6736577de4a847025ce5b0c468d1"

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "alsa"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed7572b7ba83a31e20d1b48970ee402d2e3e0537dcfe0a3ff4d6eb7508617d43"
dependencies = [
 "alsa-sys",
 "bitflags 2.9.0",
 "cfg-if",
 "libc",
]

[[package]]
name = "alsa-sys"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db8fee663d06c4e303404ef5f40488a53e062f89ba8bfed81f42325aafad1527"
dependencies = [
 "libc",
 "pkg-config",
]

[[package]]
name = "ambient-authority"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9d4ee0d472d1cd2e28c97dfa124b3d8d992e10eb0a035f33f5d12e3a177ba3b"

[[package]]
name = "ammonia"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ada2ee439075a3e70b6992fce18ac4e407cd05aea9ca3f75d2c0b0c20bbb364"
dependencies = [
 "cssparser",
 "html5ever 0.31.0",
 "maplit",
 "tendril",
 "url",
]

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "anes"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b46cbb362ab8752921c97e041f5e366ee6297bd428a31275b9fcf1e380f7299"

[[package]]
name = "anstream"
version = "0.6.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8acc5369981196006228e28809f761875c0327210a891e941f4c683b3a99529b"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "is_terminal_polyfill",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55cc3b69f167a1ef2e161439aa98aed94e6028e5f9a59be9a6ffb47aef1651f9"

[[package]]
name = "anstyle-parse"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b2d16507662817a6a20a9ea92df6652ee4f94f914589377d69f3b21bc5798a9"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79947af37f4177cfead1110013d678905c37501914fba0efea834c3fe9a8d60c"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3534e77181a9cc07539ad51f2141fe32f6c3ffd4df76db8ad92346b003ae4e"
dependencies = [
 "anstyle",
 "once_cell",
 "windows-sys 0.59.0",
]

[[package]]
name = "anthropic"
version = "0.1.0"
dependencies = [
 "anyhow",
 "chrono",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "strum 0.27.1",
 "thiserror 2.0.12",
 "workspace-hack",
]

[[package]]
name = "any_vec"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34cd60c5e3152cef0a592f1b296f1cc93715d89d2551d85315828c3a09575ff4"

[[package]]
name = "anyhow"
version = "1.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16d2d3311acee920a9eb8d33b8cbc1787ce4a264e85f964c2404b969bdcd487"

[[package]]
name = "approx"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cab112f0a86d568ea0e627cc1d6be74a1e9cd55214684db5561995f6dad897c6"
dependencies = [
 "num-traits",
]

[[package]]
name = "arbitrary"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dde20b3d026af13f561bdd0f15edf01fc734f0dafcedbaf42bba506a9517f223"

[[package]]
name = "arc-swap"
version = "1.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69f7f8c3906b62b754cd5326047894316021dcfe5a194c8ea52bdd94934a3457"

[[package]]
name = "arg_enum_proc_macro"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ae92a5119aa49cdbcf6b9f893fe4e1d98b04ccbf82ee0584ad948a44a734dea"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "arraydeque"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d902e3d592a523def97af8f317b08ce16b7ab854c1985a0c671e6f15cebc236"

[[package]]
name = "arrayref"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76a2e8124351fda1ef8aaaa3bbd7ebbcb486bbcd4225aca0aa0d84bb2db8fecb"

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"
dependencies = [
 "serde",
]

[[package]]
name = "as-raw-xcb-connection"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "175571dd1d178ced59193a6fc02dde1b972eb0bc56c892cde9beeceac5bf0f6b"

[[package]]
name = "ascii"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d92bec98840b8f03a5ff5413de5293bfcd8bf96467cf5452609f939ec6f5de16"

[[package]]
name = "ash"
version = "0.38.0+1.3.281"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bb44936d800fea8f016d7f2311c6a4f97aebd5dc86f09906139ec848cf3a46f"
dependencies = [
 "libloading",
]

[[package]]
name = "ash-window"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52bca67b61cb81e5553babde81b8211f713cb6db79766f80168f3e5f40ea6c82"
dependencies = [
 "ash",
 "raw-window-handle",
 "raw-window-metal",
]

[[package]]
name = "ashpd"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6cbdf310d77fd3aaee6ea2093db7011dc2d35d2eb3481e5607f1f8d942ed99df"
dependencies = [
 "async-fs",
 "async-net",
 "enumflags2",
 "futures-channel",
 "futures-util",
 "rand 0.9.1",
 "serde",
 "serde_repr",
 "url",
 "zbus",
]

[[package]]
name = "askpass"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "gpui",
 "shlex",
 "smol",
 "tempfile",
 "util",
 "workspace-hack",
]

[[package]]
name = "assets"
version = "0.1.0"
dependencies = [
 "anyhow",
 "gpui",
 "rust-embed",
 "workspace-hack",
]

[[package]]
name = "assistant_context_editor"
version = "0.1.0"
dependencies = [
 "agent_settings",
 "anyhow",
 "assistant_slash_command",
 "assistant_slash_commands",
 "chrono",
 "client",
 "clock",
 "collections",
 "context_server",
 "editor",
 "feature_flags",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "indexed_docs",
 "indoc",
 "language",
 "language_model",
 "languages",
 "log",
 "multi_buffer",
 "open_ai",
 "ordered-float 2.10.1",
 "parking_lot",
 "paths",
 "picker",
 "pretty_assertions",
 "project",
 "prompt_store",
 "proto",
 "rand 0.8.5",
 "regex",
 "rope",
 "rpc",
 "serde",
 "serde_json",
 "settings",
 "smallvec",
 "smol",
 "telemetry_events",
 "text",
 "theme",
 "tree-sitter-md",
 "ui",
 "unindent",
 "util",
 "uuid",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zed_llm_client",
]

[[package]]
name = "assistant_slash_command"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "collections",
 "derive_more",
 "extension",
 "futures 0.3.31",
 "gpui",
 "language",
 "language_model",
 "parking_lot",
 "pretty_assertions",
 "serde",
 "serde_json",
 "ui",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "assistant_slash_commands"
version = "0.1.0"
dependencies = [
 "anyhow",
 "assistant_slash_command",
 "cargo_toml",
 "chrono",
 "collections",
 "context_server",
 "editor",
 "feature_flags",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "globset",
 "gpui",
 "html_to_markdown",
 "http_client",
 "indexed_docs",
 "language",
 "pretty_assertions",
 "project",
 "prompt_store",
 "rope",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "text",
 "toml 0.8.20",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "worktree",
 "zlog",
]

[[package]]
name = "assistant_tool"
version = "0.1.0"
dependencies = [
 "anyhow",
 "buffer_diff",
 "clock",
 "collections",
 "ctor",
 "derive_more",
 "futures 0.3.31",
 "gpui",
 "icons",
 "language",
 "language_model",
 "log",
 "parking_lot",
 "pretty_assertions",
 "project",
 "rand 0.8.5",
 "regex",
 "serde",
 "serde_json",
 "settings",
 "text",
 "util",
 "watch",
 "workspace",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "assistant_tools"
version = "0.1.0"
dependencies = [
 "agent_settings",
 "anyhow",
 "assistant_tool",
 "buffer_diff",
 "chrono",
 "client",
 "clock",
 "collections",
 "component",
 "derive_more",
 "editor",
 "feature_flags",
 "fs",
 "futures 0.3.31",
 "gpui",
 "gpui_tokio",
 "handlebars 4.5.0",
 "html_to_markdown",
 "http_client",
 "indoc",
 "itertools 0.14.0",
 "language",
 "language_model",
 "language_models",
 "log",
 "lsp",
 "markdown",
 "open",
 "paths",
 "portable-pty",
 "pretty_assertions",
 "project",
 "prompt_store",
 "rand 0.8.5",
 "regex",
 "reqwest_client",
 "rust-embed",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smallvec",
 "smol",
 "streaming_diff",
 "strsim",
 "task",
 "tempfile",
 "terminal",
 "terminal_view",
 "theme",
 "tree-sitter-rust",
 "ui",
 "unindent",
 "util",
 "watch",
 "web_search",
 "which 6.0.3",
 "workspace",
 "workspace-hack",
 "zed_llm_client",
 "zlog",
]

[[package]]
name = "async-attributes"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3203e79f4dd9bdda415ed03cf14dae5a2bf775c683a00f94e9cd1faf0f596e5"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "async-broadcast"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "435a87a52755b8f27fcf321ac4f04b2802e337c8c4872923137471ec39c37532"
dependencies = [
 "event-listener 5.4.0",
 "event-listener-strategy",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-channel"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81953c529336010edd6d8e358f886d9581267795c61b19475b71314bffa46d35"
dependencies = [
 "concurrent-queue",
 "event-listener 2.5.3",
 "futures-core",
]

[[package]]
name = "async-channel"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89b47800b0be77592da0afd425cc03468052844aff33b84e33cc696f64e77b6a"
dependencies = [
 "concurrent-queue",
 "event-listener-strategy",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-compat"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bab94bde396a3f7b4962e396fdad640e241ed797d4d8d77fc8c237d14c58fc0"
dependencies = [
 "futures-core",
 "futures-io",
 "once_cell",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "async-compression"
version = "0.4.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59a194f9d963d8099596278594b3107448656ba73831c9d8c783e613ce86da64"
dependencies = [
 "deflate64",
 "flate2",
 "futures-core",
 "futures-io",
 "memchr",
 "pin-project-lite",
]

[[package]]
name = "async-dispatcher"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c8bff43baa5b0ca8f8bcd7f9338f5d30fbd75236a2aa89130a7c5121a06d6ca"
dependencies = [
 "async-task",
 "futures-lite 1.13.0",
]

[[package]]
name = "async-executor"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30ca9a001c1e8ba5149f91a74362376cc6bc5b919d92d988668657bd570bdcec"
dependencies = [
 "async-task",
 "concurrent-queue",
 "fastrand 2.3.0",
 "futures-lite 2.6.0",
 "slab",
]

[[package]]
name = "async-fs"
version = "2.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebcd09b382f40fcd159c2d695175b2ae620ffa5f3bd6f664131efff4e8b9e04a"
dependencies = [
 "async-lock",
 "blocking",
 "futures-lite 2.6.0",
]

[[package]]
name = "async-global-executor"
version = "2.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05b1b633a2115cd122d73b955eadd9916c18c8f510ec9cd1686404c60ad1c29c"
dependencies = [
 "async-channel 2.3.1",
 "async-executor",
 "async-io",
 "async-lock",
 "blocking",
 "futures-lite 2.6.0",
 "once_cell",
]

[[package]]
name = "async-io"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a2b323ccce0a1d90b449fd71f2a06ca7faa7c54c2751f06c9bd851fc061059"
dependencies = [
 "async-lock",
 "cfg-if",
 "concurrent-queue",
 "futures-io",
 "futures-lite 2.6.0",
 "parking",
 "polling",
 "rustix 0.38.44",
 "slab",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "async-lock"
version = "3.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff6e472cdea888a4bd64f342f09b3f50e1886d32afe8df3d663c01140b811b18"
dependencies = [
 "event-listener 5.4.0",
 "event-listener-strategy",
 "pin-project-lite",
]

[[package]]
name = "async-net"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b948000fad4873c1c9339d60f2623323a0cfd3816e5181033c6a5cb68b2accf7"
dependencies = [
 "async-io",
 "blocking",
 "futures-lite 2.6.0",
]

[[package]]
name = "async-pipe"
version = "0.1.3"
source = "git+https://github.com/zed-industries/async-pipe-rs?rev=82d00a04211cf4e1236029aa03e6b6ce2a74c553#82d00a04211cf4e1236029aa03e6b6ce2a74c553"
dependencies = [
 "futures 0.3.31",
 "log",
]

[[package]]
name = "async-process"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63255f1dc2381611000436537bbedfe83183faa303a5a0edaf191edef06526bb"
dependencies = [
 "async-channel 2.3.1",
 "async-io",
 "async-lock",
 "async-signal",
 "async-task",
 "blocking",
 "cfg-if",
 "event-listener 5.4.0",
 "futures-lite 2.6.0",
 "rustix 0.38.44",
 "tracing",
]

[[package]]
name = "async-recursion"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7d78656ba01f1b93024b7c3a0467f1608e4be67d725749fdcd7d2c7678fd7a2"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "async-recursion"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b43422f69d8ff38f95f1b2bb76517c91589a924d1559a0e935d7c8ce0274c11"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "async-signal"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "637e00349800c0bdf8bfc21ebbc0b6524abea702b0da4168ac00d070d0c0b9f3"
dependencies = [
 "async-io",
 "async-lock",
 "atomic-waker",
 "cfg-if",
 "futures-core",
 "futures-io",
 "rustix 0.38.44",
 "signal-hook-registry",
 "slab",
 "windows-sys 0.59.0",
]

[[package]]
name = "async-std"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "730294c1c08c2e0f85759590518f6333f0d5a0a766a27d519c1b244c3dfd8a24"
dependencies = [
 "async-attributes",
 "async-channel 1.9.0",
 "async-global-executor",
 "async-io",
 "async-lock",
 "async-process",
 "crossbeam-utils",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-lite 2.6.0",
 "gloo-timers",
 "kv-log-macro",
 "log",
 "memchr",
 "once_cell",
 "pin-project-lite",
 "pin-utils",
 "slab",
 "wasm-bindgen-futures",
]

[[package]]
name = "async-stream"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b5a71a6f37880a80d1d7f19efd781e4b5de42c88f0722cc13bcb6cc2cfe8476"
dependencies = [
 "async-stream-impl",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-stream-impl"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7c24de15d275a1ecfd47a380fb4d5ec9bfe0933f309ed5e705b775596a3574d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "async-stripe"
version = "0.40.0"
source = "git+https://github.com/zed-industries/async-stripe?rev=3672dd4efb7181aa597bf580bf5a2f5d23db6735#3672dd4efb7181aa597bf580bf5a2f5d23db6735"
dependencies = [
 "chrono",
 "futures-util",
 "http-types",
 "hyper 0.14.32",
 "hyper-rustls 0.24.2",
 "serde",
 "serde_json",
 "serde_path_to_error",
 "serde_qs 0.10.1",
 "smart-default",
 "smol_str 0.1.24",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "async-tar"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a42f905d4f623faf634bbd1e001e84e0efc24694afa64be9ad239bf6ca49e1f8"
dependencies = [
 "async-std",
 "filetime",
 "libc",
 "pin-project",
 "redox_syscall 0.2.16",
 "xattr",
]

[[package]]
name = "async-task"
version = "4.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b75356056920673b02621b35afd0f7dda9306d03c79a30f5c56c44cf256e3de"

[[package]]
name = "async-trait"
version = "0.1.88"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e539d3fca749fcee5236ab05e93a52867dd549cc157c8cb7f99595f3cedffdb5"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "async-tungstenite"
version = "0.29.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef0f7efedeac57d9b26170f72965ecfd31473ca52ca7a64e925b0b6f5f079886"
dependencies = [
 "atomic-waker",
 "futures-core",
 "futures-io",
 "futures-task",
 "futures-util",
 "log",
 "pin-project-lite",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls 0.26.2",
 "tungstenite 0.26.2",
]

[[package]]
name = "async_zip"
version = "0.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b9f7252833d5ed4b00aa9604b563529dd5e11de9c23615de2dcdf91eb87b52"
dependencies = [
 "async-compression",
 "crc32fast",
 "futures-lite 2.6.0",
 "pin-project",
 "thiserror 1.0.69",
]

[[package]]
name = "asynchronous-codec"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a860072022177f903e59730004fb5dc13db9275b79bb2aef7ba8ce831956c233"
dependencies = [
 "bytes 1.10.1",
 "futures-sink",
 "futures-util",
 "memchr",
 "pin-project-lite",
]

[[package]]
name = "atoi"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f28d99ec8bfea296261ca1af174f24225171fea9664ba9003cbebee704810528"
dependencies = [
 "num-traits",
]

[[package]]
name = "atomic"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c59bdb34bc650a32731b31bd8f0829cc15d24a708ee31559e0bb34f2bc320cba"

[[package]]
name = "atomic-waker"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1505bd5d3d116872e7271a6d4e16d81d0c8570876c8de68093a09ac269d8aac0"

[[package]]
name = "audio"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "derive_more",
 "gpui",
 "parking_lot",
 "rodio",
 "util",
 "workspace-hack",
]

[[package]]
name = "auditable-serde"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c7bf8143dfc3c0258df908843e169b5cc5fcf76c7718bd66135ef4a9cd558c5"
dependencies = [
 "semver",
 "serde",
 "serde_json",
 "topological-sort",
]

[[package]]
name = "auto_update"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "db",
 "gpui",
 "http_client",
 "log",
 "paths",
 "release_channel",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "tempfile",
 "which 6.0.3",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "auto_update_helper"
version = "0.1.0"
dependencies = [
 "anyhow",
 "log",
 "simplelog",
 "windows 0.61.1",
 "winresource",
 "workspace-hack",
]

[[package]]
name = "auto_update_ui"
version = "0.1.0"
dependencies = [
 "anyhow",
 "auto_update",
 "client",
 "editor",
 "gpui",
 "http_client",
 "markdown_preview",
 "release_channel",
 "serde",
 "serde_json",
 "smol",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "autocfg"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ace50bade8e6234aa140d9a2f552bbee1db4d353f69b8217bc503490fc1a9f26"

[[package]]
name = "av1-grain"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6678909d8c5d46a42abcf571271e15fdbc0a225e3646cf23762cd415046c78bf"
dependencies = [
 "anyhow",
 "arrayvec",
 "log",
 "nom",
 "num-rational",
 "v_frame",
]

[[package]]
name = "avif-serialize"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98922d6a4cfbcb08820c69d8eeccc05bb1f29bfa06b4f5b1dbfe9a868bd7608e"
dependencies = [
 "arrayvec",
]

[[package]]
name = "aws-config"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c39646d1a6b51240a1a23bb57ea4eebede7e16fbc237fdc876980233dcecb4f"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-sdk-sso",
 "aws-sdk-ssooidc",
 "aws-sdk-sts",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "hex",
 "http 1.3.1",
 "ring",
 "time",
 "tokio",
 "tracing",
 "url",
 "zeroize",
]

[[package]]
name = "aws-credential-types"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4471bef4c22a06d2c7a1b6492493d3fdf24a805323109d6874f9c94d5906ac14"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "zeroize",
]

[[package]]
name = "aws-lc-rs"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fcc8f365936c834db5514fc45aee5b1202d677e6b40e48468aaaa8183ca8c7"
dependencies = [
 "aws-lc-sys",
 "zeroize",
]

[[package]]
name = "aws-lc-sys"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61b1d86e7705efe1be1b569bab41d4fa1e14e220b60a160f78de2db687add079"
dependencies = [
 "bindgen 0.69.5",
 "cc",
 "cmake",
 "dunce",
 "fs_extra",
]

[[package]]
name = "aws-runtime"
version = "1.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0aff45ffe35196e593ea3b9dd65b320e51e2dda95aff4390bc459e461d09c6ad"
dependencies = [
 "aws-credential-types",
 "aws-sigv4",
 "aws-smithy-async",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "http 0.2.12",
 "http-body 0.4.6",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "tracing",
 "uuid",
]

[[package]]
name = "aws-sdk-bedrockruntime"
version = "1.82.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cb95f77abd4321348dd2f52a25e1de199732f54d2a35860ad20f5df21c66b44"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-sigv4",
 "aws-smithy-async",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "http 0.2.12",
 "hyper 0.14.32",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-kinesis"
version = "1.66.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e43e5fb05c78cdad4fef5be4503465e4b42292f472fc991823ea4c50078208e4"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-s3"
version = "1.82.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6eab2900764411ab01c8e91a76fd11a63b4e12bc3da97d9e14a0ce1343d86d3"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-sigv4",
 "aws-smithy-async",
 "aws-smithy-checksums",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-smithy-xml",
 "aws-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "hex",
 "hmac",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "lru",
 "once_cell",
 "percent-encoding",
 "regex-lite",
 "sha2",
 "tracing",
 "url",
]

[[package]]
name = "aws-sdk-sso"
version = "1.64.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02d4bdb0e5f80f0689e61c77ab678b2b9304af329616af38aef5b6b967b8e736"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-ssooidc"
version = "1.65.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acbbb3ce8da257aedbccdcb1aadafbbb6a5fe9adf445db0e1ea897bdc7e22d08"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-sts"
version = "1.65.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96a78a8f50a1630db757b60f679c8226a8a70ee2ab5f5e6e51dc67f6c61c7cfd"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-query",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-smithy-xml",
 "aws-types",
 "fastrand 2.3.0",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sigv4"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69d03c3c05ff80d54ff860fe38c726f6f494c639ae975203a101335f223386db"
dependencies = [
 "aws-credential-types",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "bytes 1.10.1",
 "crypto-bigint 0.5.5",
 "form_urlencoded",
 "hex",
 "hmac",
 "http 0.2.12",
 "http 1.3.1",
 "once_cell",
 "p256",
 "percent-encoding",
 "ring",
 "sha2",
 "subtle",
 "time",
 "tracing",
 "zeroize",
]

[[package]]
name = "aws-smithy-async"
version = "1.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e190749ea56f8c42bf15dd76c65e14f8f765233e6df9b0506d9d934ebef867c"
dependencies = [
 "futures-util",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "aws-smithy-checksums"
version = "0.63.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b65d21e1ba6f2cdec92044f904356a19f5ad86961acf015741106cdfafd747c0"
dependencies = [
 "aws-smithy-http",
 "aws-smithy-types",
 "bytes 1.10.1",
 "crc32c",
 "crc32fast",
 "crc64fast-nvme",
 "hex",
 "http 0.2.12",
 "http-body 0.4.6",
 "md-5",
 "pin-project-lite",
 "sha1",
 "sha2",
 "tracing",
]

[[package]]
name = "aws-smithy-eventstream"
version = "0.60.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c45d3dddac16c5c59d553ece225a88870cf81b7b813c9cc17b78cf4685eac7a"
dependencies = [
 "aws-smithy-types",
 "bytes 1.10.1",
 "crc32fast",
]

[[package]]
name = "aws-smithy-http"
version = "0.62.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5949124d11e538ca21142d1fba61ab0a2a2c1bc3ed323cdb3e4b878bfb83166"
dependencies = [
 "aws-smithy-eventstream",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "bytes 1.10.1",
 "bytes-utils",
 "futures-core",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "pin-utils",
 "tracing",
]

[[package]]
name = "aws-smithy-http-client"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8aff1159006441d02e57204bf57a1b890ba68bedb6904ffd2873c1c4c11c546b"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "h2 0.4.9",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "hyper 0.14.32",
 "hyper 1.6.0",
 "hyper-rustls 0.24.2",
 "hyper-rustls 0.27.5",
 "hyper-util",
 "pin-project-lite",
 "rustls 0.21.12",
 "rustls 0.23.26",
 "rustls-native-certs 0.8.1",
 "rustls-pki-types",
 "tokio",
 "tower 0.5.2",
 "tracing",
]

[[package]]
name = "aws-smithy-json"
version = "0.61.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92144e45819cae7dc62af23eac5a038a58aa544432d2102609654376a900bd07"
dependencies = [
 "aws-smithy-types",
]

[[package]]
name = "aws-smithy-observability"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "445d065e76bc1ef54963db400319f1dd3ebb3e0a74af20f7f7630625b0cc7cc0"
dependencies = [
 "aws-smithy-runtime-api",
 "once_cell",
]

[[package]]
name = "aws-smithy-query"
version = "0.60.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2fbd61ceb3fe8a1cb7352e42689cec5335833cd9f94103a61e98f9bb61c64bb"
dependencies = [
 "aws-smithy-types",
 "urlencoding",
]

[[package]]
name = "aws-smithy-runtime"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0152749e17ce4d1b47c7747bdfec09dac1ccafdcbc741ebf9daa2a373356730f"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-http-client",
 "aws-smithy-observability",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "bytes 1.10.1",
 "fastrand 2.3.0",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "http-body 1.0.1",
 "once_cell",
 "pin-project-lite",
 "pin-utils",
 "tokio",
 "tracing",
]

[[package]]
name = "aws-smithy-runtime-api"
version = "1.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3da37cf5d57011cb1753456518ec76e31691f1f474b73934a284eb2a1c76510f"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-types",
 "bytes 1.10.1",
 "http 0.2.12",
 "http 1.3.1",
 "pin-project-lite",
 "tokio",
 "tracing",
 "zeroize",
]

[[package]]
name = "aws-smithy-types"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "836155caafba616c0ff9b07944324785de2ab016141c3550bd1c07882f8cee8f"
dependencies = [
 "base64-simd",
 "bytes 1.10.1",
 "bytes-utils",
 "futures-core",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "http-body 1.0.1",
 "http-body-util",
 "itoa",
 "num-integer",
 "pin-project-lite",
 "pin-utils",
 "ryu",
 "serde",
 "time",
 "tokio",
 "tokio-util",
]

[[package]]
name = "aws-smithy-xml"
version = "0.60.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab0b0166827aa700d3dc519f72f8b3a91c35d0b8d042dc5d643a91e6f80648fc"
dependencies = [
 "xmlparser",
]

[[package]]
name = "aws-types"
version = "1.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3873f8deed8927ce8d04487630dc9ff73193bab64742a61d050e57a68dec4125"
dependencies = [
 "aws-credential-types",
 "aws-smithy-async",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "rustc_version",
 "tracing",
]

[[package]]
name = "aws_http_client"
version = "0.1.0"
dependencies = [
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "futures 0.3.31",
 "http_client",
 "tokio",
 "workspace-hack",
]

[[package]]
name = "axum"
version = "0.6.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b829e4e32b91e643de6eafe82b1d90675f5874230191a4ffbc1b336dec4d6bf"
dependencies = [
 "async-trait",
 "axum-core",
 "base64 0.21.7",
 "bitflags 1.3.2",
 "bytes 1.10.1",
 "futures-util",
 "headers",
 "http 0.2.12",
 "http-body 0.4.6",
 "hyper 0.14.32",
 "itoa",
 "matchit",
 "memchr",
 "mime",
 "percent-encoding",
 "pin-project-lite",
 "rustversion",
 "serde",
 "serde_json",
 "serde_path_to_error",
 "serde_urlencoded",
 "sha1",
 "sync_wrapper 0.1.2",
 "tokio",
 "tokio-tungstenite 0.20.1",
 "tower 0.4.13",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "759fa577a247914fd3f7f76d62972792636412fbfd634cd452f6a385a74d2d2c"
dependencies = [
 "async-trait",
 "bytes 1.10.1",
 "futures-util",
 "http 0.2.12",
 "http-body 0.4.6",
 "mime",
 "rustversion",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-extra"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9a320103719de37b7b4da4c8eb629d4573f6bcfd3dfe80d3208806895ccf81d"
dependencies = [
 "axum",
 "bytes 1.10.1",
 "futures-util",
 "http 0.2.12",
 "mime",
 "pin-project-lite",
 "serde",
 "serde_json",
 "tokio",
 "tower 0.4.13",
 "tower-http 0.3.5",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "backtrace"
version = "0.3.74"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d82cb332cdfaed17ae235a638438ac4d4839913cc2af585c3c6746e8f8bee1a"
dependencies = [
 "addr2line",
 "cfg-if",
 "libc",
 "miniz_oxide",
 "object",
 "rustc-demangle",
 "windows-targets 0.52.6",
]

[[package]]
name = "base16ct"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "349a06037c7bf932dd7e7d1f653678b2038b9ad46a74102f1fc7bd7872678cce"

[[package]]
name = "base64"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e1b586273c5702936fe7b7d6896644d8be71e6314cfe09d3167c95f712589e8"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "base64-simd"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "339abbe78e73178762e23bea9dfd08e697eb3f3301cd4be981c0f78ba5859195"
dependencies = [
 "outref",
 "vsimd",
]

[[package]]
name = "base64ct"
version = "1.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89e25b6adfb930f02d1981565a6e5d9c547ac15a96606256d3b59040e5cd4ca3"

[[package]]
name = "bedrock"
version = "0.1.0"
dependencies = [
 "anyhow",
 "aws-sdk-bedrockruntime",
 "aws-smithy-types",
 "futures 0.3.31",
 "schemars",
 "serde",
 "serde_json",
 "strum 0.27.1",
 "thiserror 2.0.12",
 "tokio",
 "workspace-hack",
]

[[package]]
name = "beef"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a8241f3ebb85c056b509d4327ad0358fbbba6ffb340bf388f26350aeda225b1"

[[package]]
name = "bigdecimal"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a22f228ab7a1b23027ccc6c350b72868017af7ea8356fbdf19f8d991c690013"
dependencies = [
 "autocfg",
 "libm",
 "num-bigint",
 "num-integer",
 "num-traits",
 "serde",
]

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.69.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271383c67ccabffb7381723dea0672a673f292304fcb45c01cc648c7a8d58088"
dependencies = [
 "bitflags 2.9.0",
 "cexpr",
 "clang-sys",
 "itertools 0.12.1",
 "lazy_static",
 "lazycell",
 "log",
 "prettyplease",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex",
 "syn 2.0.101",
 "which 4.4.2",
]

[[package]]
name = "bindgen"
version = "0.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f49d8fed880d473ea71efb9bf597651e77201bdd4893efe54c9e5d65ae04ce6f"
dependencies = [
 "bitflags 2.9.0",
 "cexpr",
 "clang-sys",
 "itertools 0.13.0",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex",
 "syn 2.0.101",
]

[[package]]
name = "bindgen"
version = "0.71.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f58bf3d7db68cfbac37cfc485a8d711e87e064c3d0fe0435b92f7a407f9d6b3"
dependencies = [
 "bitflags 2.9.0",
 "cexpr",
 "clang-sys",
 "itertools 0.13.0",
 "log",
 "prettyplease",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 2.1.1",
 "shlex",
 "syn 2.0.101",
]

[[package]]
name = "bit-set"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0700ddab506f33b20a03b13996eccd309a48e5ff77d0d95926aa0210fb4e95f1"
dependencies = [
 "bit-vec 0.6.3",
]

[[package]]
name = "bit-set"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08807e080ed7f9d5433fa9b275196cfc35414f66a0c79d864dc51a0d825231a3"
dependencies = [
 "bit-vec 0.8.0",
]

[[package]]
name = "bit-vec"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "349f9b6a179ed607305526ca489b34ad0a41aed5f7980fa90eb03160b69598fb"

[[package]]
name = "bit-vec"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e764a1d40d510daf35e07be9eb06e75770908c27d411ee6c92109c9840eaaf7"

[[package]]
name = "bit_field"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc827186963e592360843fb5ba4b973e145841266c1357f7180c43526f2e5b61"

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c8214115b7bf84099f1309324e63141d4c5d7cc26862f97a0a857dbefe165bd"
dependencies = [
 "serde",
]

[[package]]
name = "bitstream-io"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6099cdc01846bc367c4e7dd630dc5966dccf36b652fae7a74e17b640411a91b2"

[[package]]
name = "bitvec"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc2832c24239b0141d5674bb9174f9d68a8b5b3f2753311927c172ca46f7e9c"
dependencies = [
 "funty",
 "radium",
 "tap",
 "wyz",
]

[[package]]
name = "blade-graphics"
version = "0.6.0"
source = "git+https://github.com/kvark/blade?rev=416375211bb0b5826b3584dccdb6a43369e499ad#416375211bb0b5826b3584dccdb6a43369e499ad"
dependencies = [
 "ash",
 "ash-window",
 "bitflags 2.9.0",
 "bytemuck",
 "codespan-reporting 0.11.1",
 "glow",
 "gpu-alloc",
 "gpu-alloc-ash",
 "hidden-trait",
 "js-sys",
 "khronos-egl",
 "libloading",
 "log",
 "mint",
 "naga",
 "objc2",
 "objc2-app-kit",
 "objc2-core-foundation",
 "objc2-foundation",
 "objc2-metal",
 "objc2-quartz-core",
 "objc2-ui-kit",
 "raw-window-handle",
 "slab",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "blade-macros"
version = "0.3.0"
source = "git+https://github.com/kvark/blade?rev=416375211bb0b5826b3584dccdb6a43369e499ad#416375211bb0b5826b3584dccdb6a43369e499ad"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "blade-util"
version = "0.2.0"
source = "git+https://github.com/kvark/blade?rev=416375211bb0b5826b3584dccdb6a43369e499ad#416375211bb0b5826b3584dccdb6a43369e499ad"
dependencies = [
 "blade-graphics",
 "bytemuck",
 "log",
 "profiling",
]

[[package]]
name = "blake2"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46502ad458c9a52b69d4d4d32775c788b7a1b85e8bc9d482d92250fc0e3f8efe"
dependencies = [
 "digest",
]

[[package]]
name = "blake3"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3888aaa89e4b2a40fca9848e400f6a658a5a3978de7be858e209cafa8be9a4a0"
dependencies = [
 "arrayref",
 "arrayvec",
 "cc",
 "cfg-if",
 "constant_time_eq 0.3.1",
]

[[package]]
name = "block"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d8c1fef690941d3e7788d328517591fecc684c084084702d6ff1641e993699a"

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array",
]

[[package]]
name = "block-padding"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8894febbff9f758034a5b8e12d87918f56dfc64a8e1fe757d65e29041538d93"
dependencies = [
 "generic-array",
]

[[package]]
name = "block2"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "340d2f0bdb2a43c1d3cd40513185b2bd7def0aa1052f956455114bc98f82dcf2"
dependencies = [
 "objc2",
]

[[package]]
name = "blocking"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "703f41c54fc768e63e091340b424302bb1c29ef4aa0c7f10fe849dfb114d29ea"
dependencies = [
 "async-channel 2.3.1",
 "async-task",
 "futures-io",
 "futures-lite 2.6.0",
 "piper",
]

[[package]]
name = "borrow-or-share"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3eeab4423108c5d7c744f4d234de88d18d636100093ae04caf4825134b9c3a32"

[[package]]
name = "borsh"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad8646f98db542e39fc66e68a20b2144f6a732636df7c2354e74645faaa433ce"
dependencies = [
 "borsh-derive",
 "cfg_aliases 0.2.1",
]

[[package]]
name = "borsh-derive"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdd1d3c0c2f5833f22386f252fe8ed005c7f59fdcddeef025c01b4c3b9fd9ac3"
dependencies = [
 "once_cell",
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "breadcrumbs"
version = "0.1.0"
dependencies = [
 "editor",
 "gpui",
 "itertools 0.14.0",
 "settings",
 "theme",
 "ui",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "bstr"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234113d19d0d7d613b40e86fb654acf958910802bcceab913a4f9e7cda03b1a4"
dependencies = [
 "memchr",
 "regex-automata 0.4.9",
 "serde",
]

[[package]]
name = "buffer_diff"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clock",
 "ctor",
 "futures 0.3.31",
 "git2",
 "gpui",
 "language",
 "log",
 "pretty_assertions",
 "rand 0.8.5",
 "rope",
 "serde_json",
 "sum_tree",
 "text",
 "unindent",
 "util",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "built"
version = "0.7.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56ed6191a7e78c36abdb16ab65341eefd73d64d303fffccdbb00d51e4205967b"

[[package]]
name = "bumpalo"
version = "3.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1628fb46dfa0b37568d12e5edd512553eccf6a22a78e8bde00bb4aed84d5bdbf"
dependencies = [
 "allocator-api2",
]

[[package]]
name = "by_address"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64fa3c856b712db6612c019f14756e64e4bcea13337a6b33b696333a9eaa2d06"

[[package]]
name = "bytecheck"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23cdc57ce23ac53c931e88a43d06d070a6fd142f2617be5855eb75efc9beb1c2"
dependencies = [
 "bytecheck_derive",
 "ptr_meta",
 "simdutf8",
]

[[package]]
name = "bytecheck_derive"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3db406d29fbcd95542e92559bed4d8ad92636d1ca8b3b72ede10b4bcc010e659"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "bytecount"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ce89b21cab1437276d2650d57e971f9d548a2d9037cc231abdc0562b97498ce"

[[package]]
name = "bytemuck"
version = "1.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6b1fc10dbac614ebc03540c9dbd60e83887fda27794998c6528f1782047d540"
dependencies = [
 "bytemuck_derive",
]

[[package]]
name = "bytemuck_derive"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ecc273b49b3205b83d648f0690daa588925572cc5063745bfe547fe7ec8e1a1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "byteorder-lite"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f1fe948ff07f4bd06c30984e69f5b4899c516a3ef74f34df92a2df2ab535495"

[[package]]
name = "bytes"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "206fdffcfa2df7cbe15601ef46c813fce0965eb3286db6b56c583b814b51c81c"
dependencies = [
 "byteorder",
 "iovec",
]

[[package]]
name = "bytes"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71b6127be86fdcfddb610f7182ac57211d4b18a3e9c82eb2d17662f2227ad6a"

[[package]]
name = "bytes-utils"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dafe3a8757b027e2be6e4e5601ed563c55989fcf1546e933c66c8eb3a058d35"
dependencies = [
 "bytes 1.10.1",
 "either",
]

[[package]]
name = "bzip2"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdb116a6ef3f6c3698828873ad02c3014b3c85cadb88496095628e3ef1e347f8"
dependencies = [
 "bzip2-sys",
 "libc",
]

[[package]]
name = "bzip2-sys"
version = "0.1.13****.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "225bff33b2141874fe80d71e07d6eec4f85c5c216453dd96388240f96e1acc14"
dependencies = [
 "cc",
 "pkg-config",
]

[[package]]
name = "call"
version = "0.1.0"
dependencies = [
 "anyhow",
 "audio",
 "client",
 "collections",
 "fs",
 "futures 0.3.31",
 "gpui",
 "gpui_tokio",
 "http_client",
 "language",
 "livekit_client",
 "log",
 "postage",
 "project",
 "schemars",
 "serde",
 "serde_derive",
 "settings",
 "telemetry",
 "util",
 "workspace-hack",
]

[[package]]
name = "calloop"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b99da2f8558ca23c71f4fd15dc57c906239752dd27ff3c00a1d56b685b7cbfec"
dependencies = [
 "bitflags 2.9.0",
 "log",
 "polling",
 "rustix 0.38.44",
 "slab",
 "thiserror 1.0.69",
]

[[package]]
name = "calloop-wayland-source"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95a66a987056935f7efce4ab5668920b5d0dac4a7c99991a67395f13702ddd20"
dependencies = [
 "calloop",
 "rustix 0.38.44",
 "wayland-backend",
 "wayland-client",
]

[[package]]
name = "camino"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b96ec4966b5813e2c0507c1f86115c8c5abaadc3980879c3424042a02fd1ad3"
dependencies = [
 "serde",
]

[[package]]
name = "cap-fs-ext"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e41cc18551193fe8fa6f15c1e3c799bc5ec9e2cfbfaa8ed46f37013e3e6c173c"
dependencies = [
 "cap-primitives",
 "cap-std",
 "io-lifetimes",
 "windows-sys 0.59.0",
]

[[package]]
name = "cap-net-ext"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f83833816c66c986e913b22ac887cec216ea09301802054316fc5301809702c"
dependencies = [
 "cap-primitives",
 "cap-std",
 "rustix 1.0.7",
 "smallvec",
]

[[package]]
name = "cap-primitives"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a1e394ed14f39f8bc26f59d4c0c010dbe7f0a1b9bafff451b1f98b67c8af62a"
dependencies = [
 "ambient-authority",
 "fs-set-times",
 "io-extras",
 "io-lifetimes",
 "ipnet",
 "maybe-owned",
 "rustix 1.0.7",
 "rustix-linux-procfs",
 "windows-sys 0.59.0",
 "winx",
]

[[package]]
name = "cap-rand"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0acb89ccf798a28683f00089d0630dfaceec087234eae0d308c05ddeaa941b40"
dependencies = [
 "ambient-authority",
 "rand 0.8.5",
]

[[package]]
name = "cap-std"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07c0355ca583dd58f176c3c12489d684163861ede3c9efa6fd8bba314c984189"
dependencies = [
 "cap-primitives",
 "io-extras",
 "io-lifetimes",
 "rustix 1.0.7",
]

[[package]]
name = "cap-time-ext"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "491af520b8770085daa0466978c75db90368c71896523f2464214e38359b1a5b"
dependencies = [
 "ambient-authority",
 "cap-primitives",
 "iana-time-zone",
 "once_cell",
 "rustix 1.0.7",
 "winx",
]

[[package]]
name = "cargo-platform"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e35af189006b9c0f00a064685c727031e3ed2d8020f7ba284d78cc2671bd36ea"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.19.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd5eb614ed4c27c5d706420e4320fbe3216ab31fa1c33cd8246ac36dae4479ba"
dependencies = [
 "camino",
 "cargo-platform",
 "semver",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "cargo_toml"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fbd1fe9db3ebf71b89060adaf7b0504c2d6a425cf061313099547e382c2e472"
dependencies = [
 "serde",
 "toml 0.8.20",
]

[[package]]
name = "cast"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37b2a672a2cb129a2e41c10b1224bb368f9f37a2b16b612598138befd7b37eb5"

[[package]]
name = "cbc"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26b52a9543ae338f279b96b0b9fed9c8093744685043739079ce85cd58f289a6"
dependencies = [
 "cipher",
]

[[package]]
name = "cbindgen"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eadd868a2ce9ca38de7eeafdcec9c7065ef89b42b32f0839278d55f35c54d1ff"
dependencies = [
 "heck 0.4.1",
 "indexmap",
 "log",
 "proc-macro2",
 "quote",
 "serde",
 "serde_json",
 "syn 2.0.101",
 "tempfile",
 "toml 0.8.20",
]

[[package]]
name = "cc"
version = "1.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e3a13707ac958681c13b39b458c073d0d9bc8a22cb1b2f4c8e55eb72c13f362"
dependencies = [
 "jobserver",
 "libc",
 "shlex",
]

[[package]]
name = "cesu8"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d43a04d8753f35258c91f8ec639f792891f748a1edbd759cf1dcea3382ad83c"

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom",
]

[[package]]
name = "cfg-expr"
version = "0.15.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d067ad48b8650848b989a59a86c6c36a995d02d2bf778d45c3c5d57bc2718f02"
dependencies = [
 "smallvec",
 "target-lexicon 0.12.16",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "cfg_aliases"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd16c4719339c4530435d38e511904438d07cce7950afa3718a84ac36c10e89e"

[[package]]
name = "cfg_aliases"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613afe47fcd5fac7ccf1db93babcb082c5994d996f20b8b159f2ad1658eb5724"

[[package]]
name = "cgl"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ced0551234e87afee12411d535648dd89d2e7f34c78b753395567aff3d447ff"
dependencies = [
 "libc",
]

[[package]]
name = "channel"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "clock",
 "collections",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "language",
 "log",
 "rand 0.8.5",
 "release_channel",
 "rpc",
 "settings",
 "sum_tree",
 "text",
 "time",
 "util",
 "workspace-hack",
]

[[package]]
name = "chrono"
version = "0.4.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c469d952047f47f91b68d1cba3f10d63c11d73e4636f24f08daf0278abf01c4d"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "serde",
 "wasm-bindgen",
 "windows-link",
]

[[package]]
name = "chunked_transfer"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e4de3bc4ea267985becf712dc6d9eed8b04c953b3fcfb339ebc87acd9804901"

[[package]]
name = "ciborium"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42e69ffd6f0917f5c029256a24d0161db17cea3997d185db0d35926308770f0e"
dependencies = [
 "ciborium-io",
 "ciborium-ll",
 "serde",
]

[[package]]
name = "ciborium-io"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05afea1e0a06c9be33d539b876f1ce3692f4afea2cb41f740e7743225ed1c757"

[[package]]
name = "ciborium-ll"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57663b653d948a338bfb3eeba9bb2fd5fcfaecb9e199e87e1eda4d9e8b240fd9"
dependencies = [
 "ciborium-io",
 "half",
]

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
 "zeroize",
]

[[package]]
name = "circular-buffer"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23bdce1da528cadbac4654b5632bfcd8c6c63e25b1d42cea919a95958790b51d"

[[package]]
name = "clang-sys"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b023947811758c97c59bf9d1c188fd619ad4718dcaa767947df1cadb14f39f4"
dependencies = [
 "glob",
 "libc",
 "libloading",
]

[[package]]
name = "clap"
version = "4.5.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eccb054f56cbd38340b380d4a8e69ef1f02f1af43db2f0cc817a4774d80ae071"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.5.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efd9466fac8543255d3b1fcad4762c5e116ffe808c8a3043d4263cd4fd4862a2"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex",
 "strsim",
 "terminal_size",
]

[[package]]
name = "clap_complete"
version = "4.5.47"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c06f5378ea264ad4f82bbc826628b5aad714a75abf6ece087e923010eb937fb6"
dependencies = [
 "clap",
]

[[package]]
name = "clap_derive"
version = "4.5.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09176aae279615badda0765c0c0b3f6ed53f4709118af73cf4655d85d1530cd7"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "clap_lex"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46ad14479a25103f283c0f10005961cf086d8dc42205bb44c46ac563475dca6"

[[package]]
name = "cli"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "collections",
 "core-foundation 0.10.0",
 "core-services",
 "exec",
 "fork",
 "ipc-channel",
 "parking_lot",
 "paths",
 "plist",
 "release_channel",
 "serde",
 "tempfile",
 "util",
 "windows 0.61.1",
 "workspace-hack",
]

[[package]]
name = "client"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-recursion 0.3.2",
 "async-tungstenite",
 "base64 0.22.1",
 "chrono",
 "clock",
 "cocoa 0.26.0",
 "collections",
 "credentials_provider",
 "feature_flags",
 "futures 0.3.31",
 "gpui",
 "gpui_tokio",
 "http_client",
 "http_client_tls",
 "httparse",
 "log",
 "parking_lot",
 "paths",
 "postage",
 "rand 0.8.5",
 "release_channel",
 "rpc",
 "rustls-pki-types",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "sha2",
 "smol",
 "telemetry",
 "telemetry_events",
 "text",
 "thiserror 2.0.12",
 "time",
 "tiny_http",
 "tokio",
 "tokio-native-tls",
 "tokio-rustls 0.26.2",
 "tokio-socks",
 "url",
 "util",
 "windows 0.61.1",
 "workspace-hack",
 "worktree",
]

[[package]]
name = "clock"
version = "0.1.0"
dependencies = [
 "parking_lot",
 "serde",
 "smallvec",
 "workspace-hack",
]

[[package]]
name = "clru"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbd0f76e066e64fdc5631e3bb46381254deab9ef1158292f27c8c57e3bf3fe59"

[[package]]
name = "cmake"
version = "0.1.54"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7caa3f9de89ddbe2c607f4101924c5abec803763ae9534e4f4d7d8f84aa81f0"
dependencies = [
 "cc",
]

[[package]]
name = "cobs"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67ba02a97a2bd10f4b59b25c7973101c79642302776489e030cd13cdab09ed15"

[[package]]
name = "cocoa"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6140449f97a6e97f9511815c5632d84c8aacf8ac271ad77c559218161a1373c"
dependencies = [
 "bitflags 1.3.2",
 "block",
 "cocoa-foundation 0.1.2",
 "core-foundation 0.9.4",
 "core-graphics 0.23.2",
 "foreign-types 0.5.0",
 "libc",
 "objc",
]

[[package]]
name = "cocoa"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f79398230a6e2c08f5c9760610eb6924b52aa9e7950a619602baba59dcbbdbb2"
dependencies = [
 "bitflags 2.9.0",
 "block",
 "cocoa-foundation 0.2.0",
 "core-foundation 0.10.0",
 "core-graphics 0.24.0",
 "foreign-types 0.5.0",
 "libc",
 "objc",
]

[[package]]
name = "cocoa-foundation"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c6234cbb2e4c785b456c0644748b1ac416dd045799740356f8363dfe00c93f7"
dependencies = [
 "bitflags 1.3.2",
 "block",
 "core-foundation 0.9.4",
 "core-graphics-types 0.1.3",
 "libc",
 "objc",
]

[[package]]
name = "cocoa-foundation"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e14045fb83be07b5acf1c0884b2180461635b433455fa35d1cd6f17f1450679d"
dependencies = [
 "bitflags 2.9.0",
 "block",
 "core-foundation 0.10.0",
 "core-graphics-types 0.2.0",
 "libc",
 "objc",
]

[[package]]
name = "codespan-reporting"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e"
dependencies = [
 "termcolor",
 "unicode-width 0.1.14",
]

[[package]]
name = "codespan-reporting"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe6d2e5af09e8c8ad56c969f2157a3d4238cebc7c55f0a517728c38f7b200f81"
dependencies = [
 "serde",
 "termcolor",
 "unicode-width 0.2.0",
]

[[package]]
name = "collab"
version = "0.44.0"
dependencies = [
 "agent_settings",
 "anyhow",
 "assistant_context_editor",
 "assistant_slash_command",
 "async-stripe",
 "async-trait",
 "async-tungstenite",
 "audio",
 "aws-config",
 "aws-sdk-kinesis",
 "aws-sdk-s3",
 "axum",
 "axum-extra",
 "base64 0.22.1",
 "buffer_diff",
 "call",
 "channel",
 "chrono",
 "client",
 "clock",
 "collab_ui",
 "collections",
 "command_palette_hooks",
 "context_server",
 "ctor",
 "dap",
 "dap_adapters",
 "dashmap 6.1.0",
 "debugger_ui",
 "derive_more",
 "editor",
 "envy",
 "extension",
 "file_finder",
 "fs",
 "futures 0.3.31",
 "git",
 "git_hosting_providers",
 "git_ui",
 "gpui",
 "gpui_tokio",
 "hex",
 "http_client",
 "hyper 0.14.32",
 "indoc",
 "jsonwebtoken",
 "language",
 "language_model",
 "livekit_api",
 "livekit_client",
 "log",
 "lsp",
 "menu",
 "multi_buffer",
 "nanoid",
 "node_runtime",
 "notifications",
 "parking_lot",
 "pretty_assertions",
 "project",
 "prometheus",
 "prompt_store",
 "prost 0.9.0",
 "rand 0.8.5",
 "recent_projects",
 "release_channel",
 "remote",
 "remote_server",
 "reqwest 0.11.27",
 "reqwest_client",
 "rpc",
 "rustc-demangle",
 "scrypt",
 "sea-orm",
 "semantic_version",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "session",
 "settings",
 "sha2",
 "sqlx",
 "strum 0.27.1",
 "subtle",
 "supermaven_api",
 "task",
 "telemetry_events",
 "text",
 "theme",
 "thiserror 2.0.12",
 "time",
 "tokio",
 "toml 0.8.20",
 "tower 0.4.13",
 "tower-http 0.4.4",
 "tracing",
 "tracing-subscriber",
 "unindent",
 "util",
 "uuid",
 "workspace",
 "workspace-hack",
 "worktree",
 "zed_llm_client",
 "zlog",
]

[[package]]
name = "collab_ui"
version = "0.1.0"
dependencies = [
 "anyhow",
 "call",
 "channel",
 "chrono",
 "client",
 "collections",
 "db",
 "editor",
 "emojis",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "http_client",
 "language",
 "log",
 "menu",
 "notifications",
 "picker",
 "pretty_assertions",
 "project",
 "release_channel",
 "rich_text",
 "rpc",
 "schemars",
 "serde",
 "serde_derive",
 "serde_json",
 "settings",
 "smallvec",
 "story",
 "telemetry",
 "theme",
 "time",
 "time_format",
 "title_bar",
 "tree-sitter-md",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "collections"
version = "0.1.0"
dependencies = [
 "indexmap",
 "rustc-hash 2.1.1",
 "workspace-hack",
]

[[package]]
name = "color_quant"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d7b894f5411737b7867f4827955924d7c254fc9f4d91a6aad6b097804b1018b"

[[package]]
name = "colorchoice"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b63caa9aa9397e2d9480a9b13673856c78d8ac123288526c37d7839f2a86990"

[[package]]
name = "combine"
version = "4.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba5a308b75df32fe02788e748662718f03fde005016435c444eea572398219fd"
dependencies = [
 "bytes 1.10.1",
 "memchr",
]

[[package]]
name = "command-fds"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ec1052629a80c28594777d1252efc8a6b005d13f9edfd8c3fc0f44d5b32489a"
dependencies = [
 "nix 0.30.1",
 "thiserror 2.0.12",
]

[[package]]
name = "command_palette"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "collections",
 "command_palette_hooks",
 "ctor",
 "db",
 "editor",
 "env_logger 0.11.8",
 "fuzzy",
 "go_to_line",
 "gpui",
 "language",
 "log",
 "menu",
 "picker",
 "postage",
 "project",
 "serde",
 "serde_json",
 "settings",
 "telemetry",
 "theme",
 "time",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "command_palette_hooks"
version = "0.1.0"
dependencies = [
 "collections",
 "derive_more",
 "gpui",
 "workspace-hack",
]

[[package]]
name = "component"
version = "0.1.0"
dependencies = [
 "collections",
 "gpui",
 "inventory",
 "parking_lot",
 "strum 0.27.1",
 "theme",
 "workspace-hack",
]

[[package]]
name = "concurrent-queue"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ca0197aee26d1ae37445ee532fefce43251d24cc7c166799f4d46817f1d3973"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "console"
version = "0.15.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "054ccb5b10f9f2cbf51eb355ca1d05c2d279ce1804688d0db74b4733a5aeafd8"
dependencies = [
 "encode_unicode",
 "libc",
 "once_cell",
 "unicode-width 0.2.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "const-oid"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2459377285ad874054d797f3ccebf984978aa39129f6eafde5cdc8315b612f8"

[[package]]
name = "const-random"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87e00182fe74b066627d63b85fd550ac2998d4b0bd86bfed477a0ae4c7c71359"
dependencies = [
 "const-random-macro",
]

[[package]]
name = "const-random-macro"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d839f2a20b0aee515dc581a6172f2321f96cab76c1a38a4c584a194955390e"
dependencies = [
 "getrandom 0.2.15",
 "once_cell",
 "tiny-keccak",
]

[[package]]
name = "constant_time_eq"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "245097e9a4535ee1e3e3931fcfcd55a796a44c643e8596ff6566d68f09b87bbc"

[[package]]
name = "constant_time_eq"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c74b8349d32d297c9134b8c88677813a227df8f779daa29bfc29c183fe3dca6"

[[package]]
name = "context_server"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "collections",
 "futures 0.3.31",
 "gpui",
 "log",
 "parking_lot",
 "postage",
 "schemars",
 "serde",
 "serde_json",
 "smol",
 "url",
 "util",
 "workspace-hack",
]

[[package]]
name = "convert_case"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6245d59a3e82a7fc217c5828a6692dbc6dfb63a0c8c90495621f7b9d79704a0e"

[[package]]
name = "convert_case"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec182b0ca2f35d8fc196cf3404988fd8b8c739a4d270ff118a398feb0cbec1ca"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "convert_case"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baaaa0ecca5b51987b9423ccdc971514dd8b0bb7b4060b983d3664dad3f1f89f"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "copilot"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-std",
 "chrono",
 "client",
 "clock",
 "collections",
 "command_palette_hooks",
 "ctor",
 "editor",
 "fs",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "indoc",
 "inline_completion",
 "itertools 0.14.0",
 "language",
 "log",
 "lsp",
 "menu",
 "node_runtime",
 "parking_lot",
 "paths",
 "project",
 "rpc",
 "serde",
 "serde_json",
 "settings",
 "task",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b55271e5c8c478ad3f38ad24ef34923091e0548492a266d19b3c0b4d82574c63"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773648b94d0e5d620f64f280777445740e61fe701025087ec8b57f45c791888b"

[[package]]
name = "core-graphics"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c07782be35f9e1140080c6b96f0d44b739e2278479f64e02fdab4e32dfd8b081"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation 0.9.4",
 "core-graphics-types 0.1.3",
 "foreign-types 0.5.0",
 "libc",
]

[[package]]
name = "core-graphics"
version = "0.24.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa95a34622365fa5bbf40b20b75dba8dfa8c94c734aea8ac9a5ca38af14316f1"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.10.0",
 "core-graphics-types 0.2.0",
 "foreign-types 0.5.0",
 "libc",
]

[[package]]
name = "core-graphics-helmer-fork"
version = "0.24.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32eb7c354ae9f6d437a6039099ce7ecd049337a8109b23d73e48e8ffba8e9cd5"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.9.4",
 "core-graphics-types 0.1.3",
 "foreign-types 0.5.0",
 "libc",
]

[[package]]
name = "core-graphics-types"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45390e6114f68f718cc7a830514a96f903cccd70d02a8f6d9f643ac4ba45afaf"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation 0.9.4",
 "libc",
]

[[package]]
name = "core-graphics-types"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d44a101f213f6c4cdc1853d4b78aef6db6bdfa3468798cc1d9912f4735013eb"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.10.0",
 "libc",
]

[[package]]
name = "core-graphics2"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e4583956b9806b69f73fcb23aee05eb3620efc282972f08f6a6db7504f8334d"
dependencies = [
 "bitflags 2.9.0",
 "block",
 "cfg-if",
 "core-foundation 0.10.0",
 "libc",
]

[[package]]
name = "core-services"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92567e81db522550ebaf742c5d875624ec7820c2c7ee5f8c60e4ce7c2ae3c0fd"
dependencies = [
 "core-foundation 0.9.4",
]

[[package]]
name = "core-text"
version = "21.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a593227b66cbd4007b2a050dfdd9e1d1318311409c8d600dc82ba1b15ca9c130"
dependencies = [
 "core-foundation 0.10.0",
 "core-graphics 0.24.0",
 "foreign-types 0.5.0",
 "libc",
]

[[package]]
name = "core-video"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d45e71d5be22206bed53c3c3cb99315fc4c3d31b8963808c6bc4538168c4f8ef"
dependencies = [
 "block",
 "core-foundation 0.10.0",
 "core-graphics2",
 "io-surface",
 "libc",
 "metal",
]

[[package]]
name = "core_maths"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77745e017f5edba1a9c1d854f6f3a52dac8a12dd5af5d2f54aecf61e43d80d30"
dependencies = [
 "libm",
]

[[package]]
name = "coreaudio-rs"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "321077172d79c662f64f5071a03120748d5bb652f5231570141be24cfcd2bace"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation-sys",
 "coreaudio-sys",
]

[[package]]
name = "coreaudio-rs"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34ca07354f6d0640333ef95f48d460a4bcf34812a7e7967f9b44c728a8f37c28"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation-sys",
 "coreaudio-sys",
]

[[package]]
name = "coreaudio-sys"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ce857aa0b77d77287acc1ac3e37a05a8c95a2af3647d23b15f263bdaeb7562b"
dependencies = [
 "bindgen 0.70.1",
]

[[package]]
name = "cosmic-text"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e1ecbb5db9a4c2ee642df67bcfa8f044dd867dbbaa21bfab139cbc204ffbf67"
dependencies = [
 "bitflags 2.9.0",
 "fontdb 0.16.2",
 "log",
 "rangemap",
 "rustc-hash 1.1.0",
 "rustybuzz 0.14.1",
 "self_cell",
 "smol_str 0.2.2",
 "swash",
 "sys-locale",
 "ttf-parser 0.21.1",
 "unicode-bidi",
 "unicode-linebreak",
 "unicode-script",
 "unicode-segmentation",
]

[[package]]
name = "cpal"
version = "0.15.3"
source = "git+https://github.com/zed-industries/cpal?rev=fd8bc2fd39f1f5fdee5a0690656caff9a26d9d50#fd8bc2fd39f1f5fdee5a0690656caff9a26d9d50"
dependencies = [
 "alsa",
 "core-foundation-sys",
 "coreaudio-rs 0.11.3",
 "dasp_sample",
 "jni",
 "js-sys",
 "libc",
 "mach2",
 "ndk",
 "ndk-context",
 "oboe",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "windows 0.54.0",
]

[[package]]
name = "cpp_demangle"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96e58d342ad113c2b878f16d5d034c03be492ae460cdbc02b7f0f2284d310c7d"
dependencies = [
 "cfg-if",
]

[[package]]
name = "cpufeatures"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59ed5838eebb26a2bb2e58f6d5b5316989ae9d08bab10e0e6d103e656d1b0280"
dependencies = [
 "libc",
]

[[package]]
name = "cranelift-bforest"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e15d04a0ce86cb36ead88ad68cf693ffd6cda47052b9e0ac114bc47fd9cd23c4"
dependencies = [
 "cranelift-entity",
]

[[package]]
name = "cranelift-bitset"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c6e3969a7ce267259ce244b7867c5d3bc9e65b0a87e81039588dfdeaede9f34"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "cranelift-codegen"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c22032c4cb42558371cf516bb47f26cdad1819d3475c133e93c49f50ebf304e"
dependencies = [
 "bumpalo",
 "cranelift-bforest",
 "cranelift-bitset",
 "cranelift-codegen-meta",
 "cranelift-codegen-shared",
 "cranelift-control",
 "cranelift-entity",
 "cranelift-isle",
 "gimli",
 "hashbrown 0.14.5",
 "log",
 "postcard",
 "regalloc2",
 "rustc-hash 2.1.1",
 "serde",
 "serde_derive",
 "sha2",
 "smallvec",
 "target-lexicon 0.13.2",
]

[[package]]
name = "cranelift-codegen-meta"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c904bc71c61b27fc57827f4a1379f29de64fe95653b620a3db77d59655eee0b8"
dependencies = [
 "cranelift-codegen-shared",
]

[[package]]
name = "cranelift-codegen-shared"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40180f5497572f644ce88c255480981ae2ec1d7bb4d8e0c0136a13b87a2f2ceb"

[[package]]
name = "cranelift-control"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d132c6d0bd8a489563472afc171759da0707804a65ece7ceb15a8c6d7dd5ef"
dependencies = [
 "arbitrary",
]

[[package]]
name = "cranelift-entity"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b2d0d9618275474fbf679dd018ac6e009acbd6ae6850f6a67be33fb3b00b323"
dependencies = [
 "cranelift-bitset",
 "serde",
 "serde_derive",
]

[[package]]
name = "cranelift-frontend"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fac41e16729107393174b0c9e3730fb072866100e1e64e80a1a963b2e484d57"
dependencies = [
 "cranelift-codegen",
 "log",
 "smallvec",
 "target-lexicon 0.13.2",
]

[[package]]
name = "cranelift-isle"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ca20d576e5070044d0a72a9effc2deacf4d6aa650403189d8ea50126483944d"

[[package]]
name = "cranelift-native"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8dee82f3f1f2c4cba9177f1cc5e350fe98764379bcd29340caa7b01f85076c7"
dependencies = [
 "cranelift-codegen",
 "libc",
 "target-lexicon 0.13.2",
]

[[package]]
name = "crc"
version = "3.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69e6e4d7b33a94f0991c26729976b10ebde1d34c3ee82408fb536164fa10d636"
dependencies = [
 "crc-catalog",
]

[[package]]
name = "crc-catalog"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19d374276b40fb8bbdee95aef7c7fa6b5316ec764510eb64b8dd0e2ed0d7e7f5"

[[package]]
name = "crc32c"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a47af21622d091a8f0fb295b88bc886ac74efcc613efc19f5d0b21de5c89e47"
dependencies = [
 "rustc_version",
]

[[package]]
name = "crc32fast"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97769d94ddab943e4510d138150169a2758b5ef3eb191a9ee688de3e23ef7b3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "crc64fast-nvme"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4955638f00a809894c947f85a024020a20815b65a5eea633798ea7924edab2b3"
dependencies = [
 "crc",
]

[[package]]
name = "credentials_provider"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "gpui",
 "paths",
 "release_channel",
 "serde",
 "serde_json",
 "workspace-hack",
]

[[package]]
name = "criterion"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2b12d017a929603d80db1831cd3a24082f8137ce19c69e6447f54f5fc8d692f"
dependencies = [
 "anes",
 "cast",
 "ciborium",
 "clap",
 "criterion-plot",
 "is-terminal",
 "itertools 0.10.5",
 "num-traits",
 "once_cell",
 "oorandom",
 "plotters",
 "rayon",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
 "tinytemplate",
 "walkdir",
]

[[package]]
name = "criterion-plot"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b50826342786a51a89e2da3a28f1c32b06e387201bc2d19791f622c673706b1"
dependencies = [
 "cast",
 "itertools 0.10.5",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82b8f8f868b36967f9606790d1903570de9ceaf870a7bf9fbbd3016d636a2cb2"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd111b7b7f7d55b72c0a6ae361660ee5853c9af73f70c3c2ef6858b950e2e51"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f58bbc28f91df819d0aa2a2c00cd19754769c2fad90579b3592b1c9ba7a3115"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a5c400df2834b80a4c3327b3aad3a4c4cd4de0629063962b03235697506a28"

[[package]]
name = "crunchy"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43da5946c66ffcc7745f48db692ffbb10a83bfe0afd96235c5c2a4fb23994929"

[[package]]
name = "crypto-bigint"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef2b4b23cddf68b89b8f8069890e8c270d54e2d5fe1b143820234805e4cb17ef"
dependencies = [
 "generic-array",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "crypto-bigint"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc92fb57ca44df6db8059111ab3af99a63d5d0f8375d9972e319a379c6bab76"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "cssparser"
version = "0.35.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e901edd733a1472f944a45116df3f846f54d37e67e68640ac8bb69689aca2aa"
dependencies = [
 "cssparser-macros",
 "dtoa-short",
 "itoa",
 "phf",
 "smallvec",
]

[[package]]
name = "cssparser-macros"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13b588ba4ac1a99f7f2964d24b3d896ddc6bf847ee3855dbd4366f058cfcd331"
dependencies = [
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "ctor"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4735f265ba6a1188052ca32d461028a7d1125868be18e287e756019da7607b5"
dependencies = [
 "ctor-proc-macro",
 "dtor",
]

[[package]]
name = "ctor-proc-macro"
version = "0.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f211af61d8efdd104f96e57adf5e426ba1bc3ed7a4ead616e15e5881fd79c4d"

[[package]]
name = "ctrlc"
version = "3.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "697b5419f348fd5ae2478e8018cb016c00a5881c7f46c717de98ffd135a5651c"
dependencies = [
 "nix 0.29.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "cursor-icon"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96a6ac251f4a2aca6b3f91340350eab87ae57c3f127ffeb585e92bd336717991"

[[package]]
name = "cxx"
version = "1.0.157"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d6354e975ea4ec28033ec3a36fa9baa1a02e3eb22ad740eeb4929370d4f5ba8"
dependencies = [
 "cc",
 "cxxbridge-cmd",
 "cxxbridge-flags",
 "cxxbridge-macro",
 "foldhash",
 "link-cplusplus",
]

[[package]]
name = "cxx-build"
version = "1.0.157"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b4400e26ea4b99417e4263b1ce2d8452404d750ba0809a7bd043072593d430d"
dependencies = [
 "cc",
 "codespan-reporting 0.12.0",
 "proc-macro2",
 "quote",
 "scratch",
 "syn 2.0.101",
]

[[package]]
name = "cxxbridge-cmd"
version = "1.0.157"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31860c98f69fc14da5742c5deaf78983e846c7b27804ca8c8319e32eef421bde"
dependencies = [
 "clap",
 "codespan-reporting 0.12.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "cxxbridge-flags"
version = "1.0.157"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0402a66013f3b8d3d9f2d7c9994656cc81e671054822b0728d7454d9231892f"

[[package]]
name = "cxxbridge-macro"
version = "1.0.157"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64c0b38f32d68f3324a981645ee39b2d686af36d03c98a386df3716108c9feae"
dependencies = [
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.101",
]

[[package]]
name = "dap"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-compression",
 "async-pipe",
 "async-tar",
 "async-trait",
 "client",
 "collections",
 "dap-types",
 "fs",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "language",
 "libc",
 "log",
 "node_runtime",
 "parking_lot",
 "paths",
 "proto",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smallvec",
 "smol",
 "task",
 "telemetry",
 "tree-sitter",
 "tree-sitter-go",
 "util",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "dap-types"
version = "0.0.1"
source = "git+https://github.com/zed-industries/dap-types?rev=b40956a7f4d1939da67429d941389ee306a3a308#b40956a7f4d1939da67429d941389ee306a3a308"
dependencies = [
 "schemars",
 "serde",
 "serde_json",
]

[[package]]
name = "dap_adapters"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "collections",
 "dap",
 "futures 0.3.31",
 "gpui",
 "json_dotpath",
 "language",
 "log",
 "paths",
 "serde",
 "serde_json",
 "task",
 "util",
 "workspace-hack",
]

[[package]]
name = "darling"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc7f46116c46ff9ab3eb1597a45688b6715c6e628b5c133e288e709a29bcb4ee"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d00b9596d185e565c2207a0b01f8bd1a135483d02d9b7b0a54b11da8d53412e"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim",
 "syn 2.0.101",
]

[[package]]
name = "darling_macro"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc34b93ccb385b40dc71c6fceac4b2ad23662c7eeb248cf10d529b7e055b6ead"
dependencies = [
 "darling_core",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "dashmap"
version = "5.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "978747c1d849a7d2ee5e8adc0159961c48fb7e5db2f06af6723b80123bb53856"
dependencies = [
 "cfg-if",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core",
]

[[package]]
name = "dashmap"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5041cc499144891f3790297212f32a74fb938e5136a14943f338ef9e0ae276cf"
dependencies = [
 "cfg-if",
 "crossbeam-utils",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core",
]

[[package]]
name = "dasp_sample"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c87e182de0887fd5361989c677c4e8f5000cd9491d6d563161a8f3a5519fc7f"

[[package]]
name = "data-encoding"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2330da5de22e8a3cb63252ce2abb30116bf5265e89c0e01bc17015ce30a476"

[[package]]
name = "data-url"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c297a1c74b71ae29df00c3e22dd9534821d60eb9af5a0192823fa2acea70c2a"

[[package]]
name = "db"
version = "0.1.0"
dependencies = [
 "anyhow",
 "gpui",
 "indoc",
 "log",
 "paths",
 "release_channel",
 "smol",
 "sqlez",
 "sqlez_macros",
 "tempfile",
 "util",
 "workspace-hack",
]

[[package]]
name = "dbus"
version = "0.9.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bb21987b9fb1613058ba3843121dd18b163b254d8a6e797e144cbac14d96d1b"
dependencies = [
 "libc",
 "libdbus-sys",
 "winapi",
]

[[package]]
name = "debug_adapter_extension"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "dap",
 "extension",
 "gpui",
 "serde_json",
 "task",
 "workspace-hack",
]

[[package]]
name = "debugger_tools"
version = "0.1.0"
dependencies = [
 "anyhow",
 "dap",
 "editor",
 "futures 0.3.31",
 "gpui",
 "project",
 "serde_json",
 "settings",
 "smol",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "debugger_ui"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "collections",
 "command_palette_hooks",
 "dap",
 "dap_adapters",
 "db",
 "debugger_tools",
 "editor",
 "feature_flags",
 "file_icons",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "itertools 0.14.0",
 "language",
 "log",
 "menu",
 "parking_lot",
 "paths",
 "picker",
 "pretty_assertions",
 "project",
 "rpc",
 "serde",
 "serde_json",
 "settings",
 "shlex",
 "sysinfo",
 "task",
 "tasks_ui",
 "terminal_view",
 "theme",
 "ui",
 "unindent",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zlog",
]

[[package]]
name = "deepseek"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "workspace-hack",
]

[[package]]
name = "deflate64"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da692b8d1080ea3045efaab14434d40468c3d8657e42abddfffca87b428f4c1b"

[[package]]
name = "der"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1a467a65c5e759bce6e65eaf91cc29f466cdc57cb65777bd646872a8a1fd4de"
dependencies = [
 "const-oid",
 "zeroize",
]

[[package]]
name = "der"
version = "0.7.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7c1832837b905bbfb5101e07cc24c8deddf52f93225eee6ead5f4d63d53ddcb"
dependencies = [
 "const-oid",
 "pem-rfc7468",
 "zeroize",
]

[[package]]
name = "deranged"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c9e6a11ca8224451684bc0d7d5a7adbf8f2fd6887261a1cfc3c0432f9d4068e"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derive_builder"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "507dfb09ea8b7fa618fcf76e953f4f5e192547945816d5358edffe39f6f94947"
dependencies = [
 "derive_builder_macro",
]

[[package]]
name = "derive_builder_core"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d5bcf7b024d6835cfb3d473887cd966994907effbe9227e8c8219824d06c4e8"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "derive_builder_macro"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab63b0e2bf4d5928aff72e83a7dace85d7bba5fe12dcc3c5a572d78caffd3f3c"
dependencies = [
 "derive_builder_core",
 "syn 2.0.101",
]

[[package]]
name = "derive_more"
version = "0.99.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3da29a38df43d6f156149c9b43ded5e018ddff2a855cf2cfd62e8cd7d079c69f"
dependencies = [
 "convert_case 0.4.0",
 "proc-macro2",
 "quote",
 "rustc_version",
 "syn 2.0.101",
]

[[package]]
name = "derive_refineable"
version = "0.1.0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "workspace-hack",
]

[[package]]
name = "diagnostics"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "collections",
 "component",
 "ctor",
 "editor",
 "futures 0.3.31",
 "gpui",
 "indoc",
 "language",
 "log",
 "lsp",
 "markdown",
 "pretty_assertions",
 "project",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "settings",
 "text",
 "theme",
 "ui",
 "unindent",
 "util",
 "workspace",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "dialoguer"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "658bce805d770f407bc62102fca7c2c64ceef2fbcb2b8bd19d2765ce093980de"
dependencies = [
 "console",
 "fuzzy-matcher",
 "shell-words",
 "tempfile",
 "thiserror 1.0.69",
 "zeroize",
]

[[package]]
name = "diff"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56254986775e3233ffa9c4d7d3faaf6d36a2c09d30b20687e9f88bc8bafc16c8"

[[package]]
name = "diffy"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b545b8c50194bdd008283985ab0b31dba153cfd5b3066a92770634fbc0d7d291"
dependencies = [
 "nu-ansi-term 0.50.1",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer",
 "const-oid",
 "crypto-common",
 "subtle",
]

[[package]]
name = "dirs"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3aa72a6f96ea37bbc5aa912f6788242832f75369bdfdadcb0e38423f100059"
dependencies = [
 "dirs-sys 0.3.7",
]

[[package]]
name = "dirs"
version = "5.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44c45a9d03d6676652bcb5e724c7e988de1acad23a711b5217ab9cbecbec2225"
dependencies = [
 "dirs-sys 0.4.1",
]

[[package]]
name = "dirs"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3e8aa94d75141228480295a7d0e7feb620b1a5ad9f12bc40be62411e38cce4e"
dependencies = [
 "dirs-sys 0.5.0",
]

[[package]]
name = "dirs-sys"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b1d1d91c932ef41c0f2663aa8b0ca0342d444d842c06914aa0a7e352d0bada6"
dependencies = [
 "libc",
 "redox_users 0.4.6",
 "winapi",
]

[[package]]
name = "dirs-sys"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "520f05a5cbd335fae5a99ff7a6ab8627577660ee5cfd6a94a6a929b52ff0321c"
dependencies = [
 "libc",
 "option-ext",
 "redox_users 0.4.6",
 "windows-sys 0.48.0",
]

[[package]]
name = "dirs-sys"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e01a3366d27ee9890022452ee61b2b63a67e6f13f58900b651ff5665f0bb1fab"
dependencies = [
 "libc",
 "option-ext",
 "redox_users 0.5.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "dispatch"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd0c93bb4b0c6d9b77f4435b0ae98c24d17f1c45b2ff844c6151a07256ca923b"

[[package]]
name = "dispatch2"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89a09f22a6c6069a18470eb92d2298acf25463f14256d24778e1230d789a2aec"
dependencies = [
 "bitflags 2.9.0",
 "objc2",
]

[[package]]
name = "displaydoc"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97369cbbc041bc366949bc74d34658d6cda5621039731c6310521892a3a20ae0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "dlib"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "330c60081dcc4c72131f8eb70510f1ac07223e5d4163db481a04a0befcffa412"
dependencies = [
 "libloading",
]

[[package]]
name = "docs_preprocessor"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "command_palette",
 "gpui",
 "mdbook",
 "regex",
 "serde",
 "serde_json",
 "settings",
 "util",
 "workspace-hack",
 "zed",
]

[[package]]
name = "documented"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc6db32f0995bc4553d2de888999075acd0dbeef75ba923503f6a724263dc6f3"
dependencies = [
 "documented-macros",
 "phf",
 "thiserror 1.0.69",
]

[[package]]
name = "documented-macros"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a394bb35929b58f9a5fd418f7c6b17a4b616efcc1e53e6995ca123948f87e5fa"
dependencies = [
 "convert_case 0.6.0",
 "itertools 0.13.0",
 "optfield",
 "proc-macro2",
 "quote",
 "strum 0.26.3",
 "syn 2.0.101",
]

[[package]]
name = "dotenv"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77c90badedccf4105eca100756a0b1289e191f6fcbdadd3cee1d2f614f97da8f"

[[package]]
name = "dotenvy"
version = "0.15.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aaf95b3e5c8f23aa320147307562d361db0ae0d51242340f558153b4eb2439b"

[[package]]
name = "downcast-rs"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b325c5dbd37f80359721ad39aca5a29fb04c89279657cffdda8736d0c0b9d2"

[[package]]
name = "doxygen-rs"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "415b6ec780d34dcf624666747194393603d0373b7141eef01d12ee58881507d9"
dependencies = [
 "phf",
]

[[package]]
name = "dtoa"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6add3b8cff394282be81f3fc1a0605db594ed69890078ca6e2cab1c408bcf04"

[[package]]
name = "dtoa-short"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd1511a7b6a56299bd043a9c167a6d2bfb37bf84a6dfceaba651168adfb43c87"
dependencies = [
 "dtoa",
]

[[package]]
name = "dtor"
version = "0.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97cbdf2ad6846025e8e25df05171abfb30e3ababa12ee0a0e44b9bbe570633a8"
dependencies = [
 "dtor-proc-macro",
]

[[package]]
name = "dtor-proc-macro"
version = "0.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7454e41ff9012c00d53cf7f475c5e3afa3b91b7c90568495495e8d9bf47a1055"

[[package]]
name = "dunce"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92773504d58c093f6de2459af4af33faa518c13451eb8f2b5698ed3d36e7c813"

[[package]]
name = "dwrote"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfe1f192fcce01590bd8d839aca53ce0d11d803bf291b2a6c4ad925a8f0024be"
dependencies = [
 "lazy_static",
 "libc",
 "winapi",
 "wio",
]

[[package]]
name = "dyn-clone"
version = "1.0.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c7a8fb8a9fbf66c1f703fe16184d10ca0ee9d23be5b4436400408ba54a95005"

[[package]]
name = "ec4rs"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b31a881d38439026e3d5dd938ab20328d36e23caca8fd5981c42e4b677f5842"

[[package]]
name = "ecdsa"
version = "0.14.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413301934810f597c1d19ca71c8710e99a3f1ba28a0d2ebc01551a2daeea3c5c"
dependencies = [
 "der 0.6.1",
 "elliptic-curve",
 "rfc6979",
 "signature 1.6.4",
]

[[package]]
name = "editor"
version = "0.1.0"
dependencies = [
 "aho-corasick",
 "anyhow",
 "assets",
 "buffer_diff",
 "client",
 "clock",
 "collections",
 "convert_case 0.8.0",
 "ctor",
 "dap",
 "db",
 "emojis",
 "feature_flags",
 "file_icons",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "git",
 "gpui",
 "http_client",
 "indoc",
 "inline_completion",
 "itertools 0.14.0",
 "language",
 "languages",
 "linkify",
 "log",
 "lsp",
 "markdown",
 "menu",
 "multi_buffer",
 "ordered-float 2.10.1",
 "parking_lot",
 "pretty_assertions",
 "project",
 "rand 0.8.5",
 "release_channel",
 "rpc",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smallvec",
 "smol",
 "snippet",
 "sum_tree",
 "task",
 "telemetry",
 "tempfile",
 "text",
 "theme",
 "time",
 "tree-sitter-html",
 "tree-sitter-python",
 "tree-sitter-rust",
 "tree-sitter-typescript",
 "ui",
 "unicode-script",
 "unicode-segmentation",
 "unindent",
 "url",
 "util",
 "uuid",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zlog",
]

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"
dependencies = [
 "serde",
]

[[package]]
name = "elasticlunr-rs"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41e83863a500656dfa214fee6682de9c5b9f03de6860fec531235ed2ae9f6571"
dependencies = [
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "elliptic-curve"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7bb888ab5300a19b8e5bceef25ac745ad065f3c9f7efc6de1b91958110891d3"
dependencies = [
 "base16ct",
 "crypto-bigint 0.4.9",
 "der 0.6.1",
 "digest",
 "ff",
 "generic-array",
 "group",
 "pkcs8 0.9.0",
 "rand_core 0.6.4",
 "sec1",
 "subtle",
 "zeroize",
]

[[package]]
name = "email_address"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e079f19b08ca6239f47f8ba8509c11cf3ea30095831f7fed61441475edd8c449"
dependencies = [
 "serde",
]

[[package]]
name = "embed-resource"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fbc6e0d8e0c03a655b53ca813f0463d2c956bc4db8138dbc89f120b066551e3"
dependencies = [
 "cc",
 "memchr",
 "rustc_version",
 "toml 0.8.20",
 "vswhom",
 "winreg 0.52.0",
]

[[package]]
name = "embedded-io"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef1a6892d9eef45c8fa6b9e0086428a2cca8491aca8f787c534a3d6d0bcb3ced"

[[package]]
name = "embedded-io"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edd0f118536f44f5ccd48bcb8b111bdc3de888b58c74639dfb034a357d0f206d"

[[package]]
name = "emojis"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99e1f1df1f181f2539bac8bf027d31ca5ffbf9e559e3f2d09413b9107b5c02f4"
dependencies = [
 "phf",
]

[[package]]
name = "encode_unicode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34aa73646ffb006b8f5147f3dc182bd4bcb190227ce861fc4a4844bf8e3cb2c0"

[[package]]
name = "encoding_rs"
version = "0.8.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75030f3c4f45dafd7586dd6780965a8c7e8e285a5ecb86713e63a79c5b2766f3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "endi"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3d8a32ae18130a3c84dd492d4215c3d913c3b07c6b63c2eb3eb7ff1101ab7bf"

[[package]]
name = "enumflags2"
version = "0.7.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba2f4b465f5318854c6f8dd686ede6c0a9dc67d4b1ac241cf0eb51521a309147"
dependencies = [
 "enumflags2_derive",
 "serde",
]

[[package]]
name = "enumflags2_derive"
version = "0.7.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc4caf64a58d7a6d65ab00639b046ff54399a39f5f2554728895ace4b297cd79"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "env_filter"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "186e05a59d4c50738528153b83b0b0194d3a29507dfec16eccd4b342903397d0"
dependencies = [
 "log",
 "regex",
]

[[package]]
name = "env_logger"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd405aab171cb85d6735e5c8d9db038c17d3ca007a4d2c25f337935c3d90580"
dependencies = [
 "humantime",
 "is-terminal",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "env_logger"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c863f0904021b108aa8b2f55046443e6b1ebde8fd4a15c399893aae4fa069f"
dependencies = [
 "anstream",
 "anstyle",
 "env_filter",
 "jiff",
 "log",
]

[[package]]
name = "envy"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f47e0157f2cb54f5ae1bd371b30a2ae4311e1c028f575cd4e81de7353215965"
dependencies = [
 "serde",
]

[[package]]
name = "equivalent"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "877a4ace8713b0bcf2a4e7eec82529c029f1d0619886d18145fea96c3ffe5c0f"

[[package]]
name = "erased-serde"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e004d887f51fcb9fef17317a2f3525c887d8aa3f4f50fed920816a688284a5b7"
dependencies = [
 "serde",
 "typeid",
]

[[package]]
name = "errno"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f639046355ee4f37944e44f60642c6f3a7efa3cf6b78c78a0d989a8ce6c396a1"
dependencies = [
 "errno-dragonfly",
 "libc",
 "winapi",
]

[[package]]
name = "errno"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "976dd42dc7e85965fe702eb8164f21f450704bdde31faefd6471dba214cb594e"
dependencies = [
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "errno-dragonfly"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa68f1b12764fab894d2755d2518754e71b4fd80ecfb822714a1206c2aab39bf"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "etagere"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc89bf99e5dc15954a60f707c1e09d7540e5cd9af85fa75caa0b510bc08c5342"
dependencies = [
 "euclid",
 "svg_fmt",
]

[[package]]
name = "etcetera"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "136d1b5283a1ab77bd9257427ffd09d8667ced0570b6f938942bc7568ed5b943"
dependencies = [
 "cfg-if",
 "home",
 "windows-sys 0.48.0",
]

[[package]]
name = "euclid"
version = "0.22.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad9cdb4b747e485a12abb0e6566612956c7a1bafa3bdb8d682c5b6d403589e48"
dependencies = [
 "num-traits",
]

[[package]]
name = "eval"
version = "0.1.0"
dependencies = [
 "agent",
 "agent_settings",
 "anyhow",
 "assistant_tool",
 "assistant_tools",
 "async-trait",
 "buffer_diff",
 "chrono",
 "clap",
 "client",
 "collections",
 "debug_adapter_extension",
 "dirs 4.0.0",
 "dotenv",
 "env_logger 0.11.8",
 "extension",
 "fs",
 "futures 0.3.31",
 "gpui",
 "gpui_tokio",
 "handlebars 4.5.0",
 "language",
 "language_extension",
 "language_model",
 "language_models",
 "languages",
 "markdown",
 "node_runtime",
 "pathdiff",
 "paths",
 "pretty_assertions",
 "project",
 "prompt_store",
 "regex",
 "release_channel",
 "reqwest_client",
 "serde",
 "serde_json",
 "settings",
 "shellexpand 2.1.2",
 "smol",
 "telemetry",
 "terminal_view",
 "toml 0.8.20",
 "unindent",
 "util",
 "uuid",
 "watch",
 "workspace-hack",
 "zed_llm_client",
]

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "event-listener"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3492acde4c3fc54c845eaab3eed8bd00c7a7d881f78bfc801e43a93dec1331ae"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8be9f3dfaaffdae2972880079a491a1a8bb7cbed0b8dd7a347f668b4150a3b93"
dependencies = [
 "event-listener 5.4.0",
 "pin-project-lite",
]

[[package]]
name = "exec"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "886b70328cba8871bfc025858e1de4be16b1d5088f2ba50b57816f4210672615"
dependencies = [
 "errno 0.2.8",
 "libc",
]

[[package]]
name = "exr"
version = "1.73.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f83197f59927b46c04a183a619b7c29df34e63e63c7869320862268c0ef687e0"
dependencies = [
 "bit_field",
 "half",
 "lebe",
 "miniz_oxide",
 "rayon-core",
 "smallvec",
 "zune-inflate",
]

[[package]]
name = "extension"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-compression",
 "async-tar",
 "async-trait",
 "collections",
 "dap",
 "fs",
 "futures 0.3.31",
 "gpui",
 "heck 0.5.0",
 "http_client",
 "language",
 "log",
 "lsp",
 "parking_lot",
 "semantic_version",
 "serde",
 "serde_json",
 "task",
 "toml 0.8.20",
 "util",
 "wasm-encoder 0.221.3",
 "wasmparser 0.221.3",
 "workspace-hack",
]

[[package]]
name = "extension_cli"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "env_logger 0.11.8",
 "extension",
 "fs",
 "gpui",
 "language",
 "log",
 "reqwest_client",
 "rpc",
 "serde",
 "serde_json",
 "theme",
 "tokio",
 "toml 0.8.20",
 "tree-sitter",
 "wasmtime",
 "workspace-hack",
]

[[package]]
name = "extension_host"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-compression",
 "async-tar",
 "async-trait",
 "client",
 "collections",
 "criterion",
 "ctor",
 "dap",
 "extension",
 "fs",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "language",
 "language_extension",
 "log",
 "lsp",
 "moka",
 "node_runtime",
 "parking_lot",
 "paths",
 "project",
 "rand 0.8.5",
 "release_channel",
 "remote",
 "reqwest_client",
 "schemars",
 "semantic_version",
 "serde",
 "serde_json",
 "serde_json_lenient",
 "settings",
 "task",
 "telemetry",
 "tempfile",
 "theme",
 "theme_extension",
 "toml 0.8.20",
 "url",
 "util",
 "wasmparser 0.221.3",
 "wasmtime",
 "wasmtime-wasi",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "extensions_ui"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "collections",
 "db",
 "editor",
 "extension",
 "extension_host",
 "fs",
 "fuzzy",
 "gpui",
 "language",
 "log",
 "num-format",
 "picker",
 "project",
 "release_channel",
 "semantic_version",
 "serde",
 "settings",
 "smallvec",
 "strum 0.27.1",
 "telemetry",
 "theme",
 "ui",
 "util",
 "vim_mode_setting",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "fallible-iterator"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2acce4a10f12dc2fb14a218589d4f1f62ef011b2d0cc4b3cb1bba8e94da14649"

[[package]]
name = "fancy-regex"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "531e46835a22af56d1e3b66f04844bed63158bc094a628bec1d321d9b4c44bf2"
dependencies = [
 "bit-set 0.5.3",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
]

[[package]]
name = "fancy-regex"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e24cb5a94bcae1e5408b0effca5cd7172ea3c5755049c5f3af4cd283a165298"
dependencies = [
 "bit-set 0.8.0",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
]

[[package]]
name = "fast-srgb8"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd2e7510819d6fbf51a5545c8f922716ecfb14df168a3242f7d33e0239efe6a1"

[[package]]
name = "faster-hex"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2a2b11eda1d40935b26cf18f6833c526845ae8c41e58d09af6adeb6f0269183"
dependencies = [
 "serde",
]

[[package]]
name = "faster-hex"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7223ae2d2f179b803433d9c830478527e92b8117eab39460edae7f1614d9fb73"
dependencies = [
 "heapless",
 "serde",
]

[[package]]
name = "fastrand"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51093e27b0797c359783294ca4f0a911c270184cb10f85783b118614a1501be"
dependencies = [
 "instant",
]

[[package]]
name = "fastrand"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37909eebbb50d72f9059c3b6d82c0463f2ff062c9e95845c43a6c9c0355411be"

[[package]]
name = "fd-lock"
version = "4.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce92ff622d6dadf7349484f42c93271a0d49b7cc4d466a936405bacbe10aa78"
dependencies = [
 "cfg-if",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "fdeflate"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e6853b52649d4ac5c0bd02320cddc5ba956bdb407c4b75a2c6b75bf51500f8c"
dependencies = [
 "simd-adler32",
]

[[package]]
name = "feature_flags"
version = "0.1.0"
dependencies = [
 "futures 0.3.31",
 "gpui",
 "smol",
 "workspace-hack",
]

[[package]]
name = "feedback"
version = "0.1.0"
dependencies = [
 "client",
 "editor",
 "gpui",
 "human_bytes",
 "menu",
 "release_channel",
 "serde",
 "sysinfo",
 "ui",
 "urlencoding",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "ff"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d013fc25338cc558c5c2cfbad646908fb23591e2404481826742b651c9af7160"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "file_finder"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "ctor",
 "editor",
 "file_icons",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "language",
 "menu",
 "picker",
 "pretty_assertions",
 "project",
 "schemars",
 "search",
 "serde",
 "serde_derive",
 "serde_json",
 "settings",
 "text",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "file_icons"
version = "0.1.0"
dependencies = [
 "gpui",
 "serde",
 "settings",
 "theme",
 "util",
 "workspace-hack",
]

[[package]]
name = "filedescriptor"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e40758ed24c9b2eeb76c35fb0aebc66c626084edd827e07e1552279814c6682d"
dependencies = [
 "libc",
 "thiserror 1.0.69",
 "winapi",
]

[[package]]
name = "filetime"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35c0522e981e68cbfa8c3f978441a5f34b30b96e146b33cd3359176b50fe8586"
dependencies = [
 "cfg-if",
 "libc",
 "libredox",
 "windows-sys 0.59.0",
]

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "flate2"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ced92e76e966ca2fd84c8f7aa01a4aea65b0eb6648d72f7c8f3e2764a67fece"
dependencies = [
 "crc32fast",
 "libz-rs-sys",
 "miniz_oxide",
]

[[package]]
name = "float-cmp"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98de4bbd547a563b716d8dfa9aad1cb19bfab00f4fa09a6a4ed21dbcf44ce9c4"

[[package]]
name = "float-ord"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ce81f49ae8a0482e4c55ea62ebbd7e5a686af544c00b9d090bba3ff9be97b3d"

[[package]]
name = "float_next_after"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bf7cc16383c4b8d58b9905a8509f02926ce3058053c056376248d958c9df1e8"

[[package]]
name = "fluent-uri"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1918b65d96df47d3591bed19c5cca17e3fa5d0707318e4b5ef2eae01764df7e5"
dependencies = [
 "borrow-or-share",
 "ref-cast",
 "serde",
]

[[package]]
name = "flume"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da0e4dd2a88388a1f4ccc7c9ce104604dab68d9f408dc34cd45823d5a9069095"
dependencies = [
 "futures-core",
 "futures-sink",
 "nanorand",
 "spin",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9c4f5dac5e15c24eb999c26181a6ca40b39fe946cbe4c263c7209467bc83af2"

[[package]]
name = "font-kit"
version = "0.14.1"
source = "git+https://github.com/zed-industries/font-kit?rev=5474cfad4b719a72ec8ed2cb7327b2b01fd10568#5474cfad4b719a72ec8ed2cb7327b2b01fd10568"
dependencies = [
 "bitflags 2.9.0",
 "byteorder",
 "core-foundation 0.10.0",
 "core-graphics 0.24.0",
 "core-text",
 "dirs 5.0.1",
 "dwrote",
 "float-ord",
 "freetype-sys",
 "lazy_static",
 "libc",
 "log",
 "pathfinder_geometry",
 "pathfinder_simd",
 "walkdir",
 "winapi",
 "yeslogic-fontconfig-sys",
]

[[package]]
name = "font-types"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fa6a5e5a77b5f3f7f9e32879f484aa5b3632ddfbe568a16266c904a6f32cdaf"
dependencies = [
 "bytemuck",
]

[[package]]
name = "fontconfig-parser"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1fcfcd44ca6e90c921fee9fa665d530b21ef1327a4c1a6c5250ea44b776ada7"
dependencies = [
 "roxmltree",
]

[[package]]
name = "fontdb"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0299020c3ef3f60f526a4f64ab4a3d4ce116b1acbf24cdd22da0068e5d81dc3"
dependencies = [
 "fontconfig-parser",
 "log",
 "memmap2",
 "slotmap",
 "tinyvec",
 "ttf-parser 0.20.0",
]

[[package]]
name = "fontdb"
version = "0.23.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "457e789b3d1202543297a350643cf459f836cade38934e7a4cf6a39e7cde2905"
dependencies = [
 "fontconfig-parser",
 "log",
 "memmap2",
 "slotmap",
 "tinyvec",
 "ttf-parser 0.25.1",
]

[[package]]
name = "foreign-types"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"
dependencies = [
 "foreign-types-shared 0.1.1",
]

[[package]]
name = "foreign-types"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d737d9aa519fb7b749cbc3b962edcf310a8dd1f4b67c91c4f83975dbdd17d965"
dependencies = [
 "foreign-types-macros",
 "foreign-types-shared 0.3.1",
]

[[package]]
name = "foreign-types-macros"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a5c6c585bc94aaf2c7b51dd4c2ba22680844aba4c687be581871a6f518c5742"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "foreign-types-shared"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"

[[package]]
name = "foreign-types-shared"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa9a19cbb55df58761df49b23516a86d432839add4af60fc256da840f66ed35b"

[[package]]
name = "fork"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05dc8b302e04a1c27f4fe694439ef0f29779ca4edc205b7b58f00db04e29656d"
dependencies = [
 "libc",
]

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "fraction"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f158e3ff0a1b334408dc9fb811cd99b446986f4d8b741bb08f9df1604085ae7"
dependencies = [
 "lazy_static",
 "num",
]

[[package]]
name = "freetype-sys"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7edc5b9669349acfda99533e9e0bcf26a51862ab43b08ee7745c55d28eb134"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
]

[[package]]
name = "fs"
version = "0.1.0"
dependencies = [
 "anyhow",
 "ashpd",
 "async-tar",
 "async-trait",
 "cocoa 0.26.0",
 "collections",
 "fsevent",
 "futures 0.3.31",
 "git",
 "gpui",
 "ignore",
 "libc",
 "log",
 "notify",
 "objc",
 "parking_lot",
 "paths",
 "proto",
 "rope",
 "serde",
 "serde_json",
 "smol",
 "tempfile",
 "text",
 "time",
 "util",
 "windows 0.61.1",
 "workspace-hack",
]

[[package]]
name = "fs-set-times"
version = "0.20.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94e7099f6313ecacbe1256e8ff9d617b75d1bcb16a6fddef94866d225a01a14a"
dependencies = [
 "io-lifetimes",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9564fc758e15025b46aa6643b1b77d047d1a56a1aea6e01002ac0c7026876213"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "fs_extra"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42703706b716c37f96a77aea830392ad231f44c9e9a67872fa5548707e11b11c"

[[package]]
name = "fsevent"
version = "0.1.0"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.10.0",
 "fsevent-sys 3.1.0",
 "parking_lot",
 "tempfile",
 "workspace-hack",
]

[[package]]
name = "fsevent-sys"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca6f5e6817058771c10f0eb0f05ddf1e35844266f972004fe8e4b21fda295bd5"
dependencies = [
 "libc",
]

[[package]]
name = "fsevent-sys"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76ee7a02da4d231650c7cea31349b889be2f45ddb3ef3032d2ec8185f6313fd2"
dependencies = [
 "libc",
]

[[package]]
name = "funty"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6d5a32815ae3f33302d95fdcb2ce17862f8c65363dcfd29360480ba1001fc9c"

[[package]]
name = "futf"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df420e2e84819663797d1ec6544b13c5be84629e7bb00dc960d6917db2987843"
dependencies = [
 "mac",
 "new_debug_unreachable",
]

[[package]]
name = "futures"
version = "0.1.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a471a38ef8ed83cd6e40aa59c1ffe17db6855c18e3604d9c4ed8c08ebc28678"

[[package]]
name = "futures"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65bc07b1a8bc7c85c5f2e110c476c7389b4554ba72af57d8445ea63a576b0876"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-batch"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f444c45a1cb86f2a7e301469fd50a82084a60dadc25d94529a8312276ecb71a"
dependencies = [
 "futures 0.3.31",
 "futures-timer",
 "pin-utils",
]

[[package]]
name = "futures-channel"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dff15bf788c671c1934e366d07e30c1814a8ef514e1af724a602e8a2fbe1b10"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f29059c0c2090612e8d742178b0580d2dc940c837851ad723096f87af6663e"

[[package]]
name = "futures-executor"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e28d1d997f585e54aebc3f97d39e72338912123a67330d723fdbb564d646c9f"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-intrusive"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d930c203dd0b6ff06e0201a4a2fe9149b43c684fd4420555b26d21b1a02956f"
dependencies = [
 "futures-core",
 "lock_api",
 "parking_lot",
]

[[package]]
name = "futures-io"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e5c1b78ca4aae1ac06c48a526a655760685149f0d465d21f37abfe57ce075c6"

[[package]]
name = "futures-lite"
version = "1.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49a9d51ce47660b1e808d3c990b4709f2f415d928835a17dfd16991515c46bce"
dependencies = [
 "fastrand 1.9.0",
 "futures-core",
 "futures-io",
 "memchr",
 "parking",
 "pin-project-lite",
 "waker-fn",
]

[[package]]
name = "futures-lite"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5edaec856126859abb19ed65f39e90fea3a9574b9707f13539acf4abf7eb532"
dependencies = [
 "fastrand 2.3.0",
 "futures-core",
 "futures-io",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "futures-macro"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "162ee34ebcb7c64a8abebc059ce0fee27c2262618d7b60ed8faf72fef13c3650"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "futures-sink"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e575fab7d1e0dcb8d0c7bcf9a63ee213816ab51902e6d244a95819acacf1d4f7"

[[package]]
name = "futures-task"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f90f7dce0722e95104fcb095585910c0977252f286e354b5e3bd38902cd99988"

[[package]]
name = "futures-timer"
version = "3.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f288b0a4f20f9a56b5d1da57e2227c661b7b16168e2f72365f57b63326e29b24"

[[package]]
name = "futures-util"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fa08315bb612088cc391249efdc3bc77536f16c91f6cf495e6fbe85b20a4a81"
dependencies = [
 "futures 0.1.31",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
 "tokio-io",
]

[[package]]
name = "fuzzy"
version = "0.1.0"
dependencies = [
 "gpui",
 "log",
 "util",
 "workspace-hack",
]

[[package]]
name = "fuzzy-matcher"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54614a3312934d066701a80f20f15fa3b56d67ac7722b39eea5b4c9dd1d66c94"
dependencies = [
 "thread_local",
]

[[package]]
name = "generator"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d18470a76cb7f8ff746cf1f7470914f900252ec36bbc40b569d74b1258446827"
dependencies = [
 "cc",
 "cfg-if",
 "libc",
 "log",
 "rustversion",
 "windows 0.61.1",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "typenum",
 "version_check",
]

[[package]]
name = "gethostname"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0176e0459c2e4a1fe232f984bca6890e681076abb9934f6cea7c326f3fc47818"
dependencies = [
 "libc",
 "windows-targets 0.48.5",
]

[[package]]
name = "getrandom"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fc3cb4d91f53b50155bdcfd23f6a4c39ae1969c2ae85982b135750cccaf5fce"
dependencies = [
 "cfg-if",
 "libc",
 "wasi 0.9.0+wasi-snapshot-preview1",
]

[[package]]
name = "getrandom"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4567c8db10ae91089c99af84c68c38da3ec2f087c3f82960bcdbf3656b6f4d7"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73fea8450eea4bac3940448fb7ae50d91f034f941199fcd9d909a5a07aa455f0"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "r-efi",
 "wasi 0.14.2+wasi-0.2.4",
 "wasm-bindgen",
]

[[package]]
name = "gif"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fb2d69b19215e18bb912fa30f7ce15846e301408695e44e0ef719f1da9e19f2"
dependencies = [
 "color_quant",
 "weezl",
]

[[package]]
name = "gimli"
version = "0.31.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07e28edb80900c19c28f1072f2e8aeca7fa06b23cd4169cefe1af5aa3260783f"
dependencies = [
 "fallible-iterator",
 "indexmap",
 "stable_deref_trait",
]

[[package]]
name = "git"
version = "0.1.0"
dependencies = [
 "anyhow",
 "askpass",
 "async-trait",
 "collections",
 "derive_more",
 "futures 0.3.31",
 "git2",
 "gpui",
 "http_client",
 "log",
 "parking_lot",
 "pretty_assertions",
 "regex",
 "rope",
 "schemars",
 "serde",
 "serde_json",
 "smol",
 "sum_tree",
 "tempfile",
 "text",
 "thiserror 2.0.12",
 "time",
 "unindent",
 "url",
 "util",
 "uuid",
 "workspace-hack",
]

[[package]]
name = "git2"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5220b8ba44c68a9a7f7a7659e864dd73692e417ef0211bea133c7b74e031eeb9"
dependencies = [
 "bitflags 2.9.0",
 "libc",
 "libgit2-sys",
 "log",
 "url",
]

[[package]]
name = "git_hosting_providers"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "futures 0.3.31",
 "git",
 "gpui",
 "http_client",
 "indoc",
 "pretty_assertions",
 "regex",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "url",
 "util",
 "workspace-hack",
]

[[package]]
name = "git_ui"
version = "0.1.0"
dependencies = [
 "agent_settings",
 "anyhow",
 "askpass",
 "buffer_diff",
 "chrono",
 "collections",
 "command_palette_hooks",
 "component",
 "ctor",
 "db",
 "editor",
 "futures 0.3.31",
 "fuzzy",
 "git",
 "gpui",
 "itertools 0.14.0",
 "language",
 "language_model",
 "linkify",
 "log",
 "markdown",
 "menu",
 "multi_buffer",
 "notifications",
 "panel",
 "picker",
 "postage",
 "pretty_assertions",
 "project",
 "schemars",
 "serde",
 "serde_derive",
 "serde_json",
 "settings",
 "strum 0.27.1",
 "telemetry",
 "theme",
 "time",
 "time_format",
 "ui",
 "unindent",
 "util",
 "windows 0.61.1",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zed_llm_client",
 "zlog",
]

[[package]]
name = "gix"
version = "0.71.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a61e71ec6817fc3c9f12f812682cfe51ee6ea0d2e27e02fc3849c35524617435"
dependencies = [
 "gix-actor",
 "gix-attributes",
 "gix-command",
 "gix-commitgraph",
 "gix-config",
 "gix-date",
 "gix-diff",
 "gix-discover",
 "gix-features 0.41.1",
 "gix-filter",
 "gix-fs 0.14.0",
 "gix-glob",
 "gix-hash 0.17.0",
 "gix-hashtable",
 "gix-ignore",
 "gix-index",
 "gix-lock",
 "gix-object",
 "gix-odb",
 "gix-pack",
 "gix-path",
 "gix-pathspec",
 "gix-protocol",
 "gix-ref",
 "gix-refspec",
 "gix-revision",
 "gix-revwalk",
 "gix-sec",
 "gix-shallow",
 "gix-submodule",
 "gix-tempfile",
 "gix-trace",
 "gix-traverse",
 "gix-url",
 "gix-utils 0.2.0",
 "gix-validate 0.9.4",
 "gix-worktree",
 "once_cell",
 "smallvec",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-actor"
version = "0.34.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f438c87d4028aca4b82f82ba8d8ab1569823cfb3e5bc5fa8456a71678b2a20e7"
dependencies = [
 "bstr",
 "gix-date",
 "gix-utils 0.2.0",
 "itoa",
 "thiserror 2.0.12",
 "winnow",
]

[[package]]
name = "gix-attributes"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4e25825e0430aa11096f8b65ced6780d4a96a133f81904edceebb5344c8dd7f"
dependencies = [
 "bstr",
 "gix-glob",
 "gix-path",
 "gix-quote",
 "gix-trace",
 "kstring",
 "smallvec",
 "thiserror 2.0.12",
 "unicode-bom",
]

[[package]]
name = "gix-bitmap"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1db9765c69502650da68f0804e3dc2b5f8ccc6a2d104ca6c85bc40700d37540"
dependencies = [
 "thiserror 2.0.12",
]

[[package]]
name = "gix-chunk"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b1f1d8764958699dc764e3f727cef280ff4d1bd92c107bbf8acd85b30c1bd6f"
dependencies = [
 "thiserror 2.0.12",
]

[[package]]
name = "gix-command"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0378995847773a697f8e157fe2963ecf3462fe64be05b7b3da000b3b472def8"
dependencies = [
 "bstr",
 "gix-path",
 "gix-quote",
 "gix-trace",
 "shell-words",
]

[[package]]
name = "gix-commitgraph"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "043cbe49b7a7505150db975f3cb7c15833335ac1e26781f615454d9d640a28fe"
dependencies = [
 "bstr",
 "gix-chunk",
 "gix-hash 0.17.0",
 "memmap2",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-config"
version = "0.44.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c6f830bf746604940261b49abf7f655d2c19cadc9f4142ae9379e3a316e8cfa"
dependencies = [
 "bstr",
 "gix-config-value",
 "gix-features 0.41.1",
 "gix-glob",
 "gix-path",
 "gix-ref",
 "gix-sec",
 "memchr",
 "once_cell",
 "smallvec",
 "thiserror 2.0.12",
 "unicode-bom",
 "winnow",
]

[[package]]
name = "gix-config-value"
version = "0.14.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8dc2c844c4cf141884678cabef736fd91dd73068b9146e6f004ba1a0457944b6"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "gix-path",
 "libc",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-date"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daa30058ec7d3511fbc229e4f9e696a35abd07ec5b82e635eff864a2726217e4"
dependencies = [
 "bstr",
 "itoa",
 "jiff",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-diff"
version = "0.51.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2c975dad2afc85e4e233f444d1efbe436c3cdcf3a07173984509c436d00a3f8"
dependencies = [
 "bstr",
 "gix-command",
 "gix-filter",
 "gix-fs 0.14.0",
 "gix-hash 0.17.0",
 "gix-object",
 "gix-path",
 "gix-tempfile",
 "gix-trace",
 "gix-traverse",
 "gix-worktree",
 "imara-diff",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-discover"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7fb8a4349b854506a3915de18d3341e5f1daa6b489c8affc9ca0d69efe86781"
dependencies = [
 "bstr",
 "dunce",
 "gix-fs 0.14.0",
 "gix-hash 0.17.0",
 "gix-path",
 "gix-ref",
 "gix-sec",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-features"
version = "0.41.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "016d6050219458d14520fe22bdfdeb9cb71631dec9bc2724767c983f60109634"
dependencies = [
 "crc32fast",
 "crossbeam-channel",
 "flate2",
 "gix-path",
 "gix-trace",
 "gix-utils 0.2.0",
 "libc",
 "once_cell",
 "parking_lot",
 "prodash",
 "thiserror 2.0.12",
 "walkdir",
]

[[package]]
name = "gix-features"
version = "0.42.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56f4399af6ec4fd9db84dd4cf9656c5c785ab492ab40a7c27ea92b4241923fed"
dependencies = [
 "gix-trace",
 "gix-utils 0.3.0",
 "libc",
 "prodash",
]

[[package]]
name = "gix-filter"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb2b2bbffdc5cc9b2b82fc82da1b98163c9b423ac2b45348baa83a947ac9ab89"
dependencies = [
 "bstr",
 "encoding_rs",
 "gix-attributes",
 "gix-command",
 "gix-hash 0.17.0",
 "gix-object",
 "gix-packetline-blocking",
 "gix-path",
 "gix-quote",
 "gix-trace",
 "gix-utils 0.2.0",
 "smallvec",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-fs"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "951e886120dc5fa8cac053e5e5c89443f12368ca36811b2e43d1539081f9c111"
dependencies = [
 "bstr",
 "fastrand 2.3.0",
 "gix-features 0.41.1",
 "gix-path",
 "gix-utils 0.2.0",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-fs"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67a0637149b4ef24d3ea55f81f77231401c8463fae6da27331c987957eb597c7"
dependencies = [
 "bstr",
 "fastrand 2.3.0",
 "gix-features 0.42.1",
 "gix-path",
 "gix-utils 0.3.0",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-glob"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20972499c03473e773a2099e5fd0c695b9b72465837797a51a43391a1635a030"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "gix-features 0.41.1",
 "gix-path",
]

[[package]]
name = "gix-hash"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "834e79722063958b03342edaa1e17595cd2939bb2b3306b3225d0815566dcb49"
dependencies = [
 "faster-hex 0.9.0",
 "gix-features 0.41.1",
 "sha1-checked",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-hash"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d4900562c662852a6b42e2ef03442eccebf24f047d8eab4f23bc12ef0d785d8"
dependencies = [
 "faster-hex 0.10.0",
 "gix-features 0.42.1",
 "sha1-checked",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-hashtable"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5b5cb3c308b4144f2612ff64e32130e641279fcf1a84d8d40dad843b4f64904"
dependencies = [
 "gix-hash 0.18.0",
 "hashbrown 0.14.5",
 "parking_lot",
]

[[package]]
name = "gix-ignore"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a27c8380f493a10d1457f756a3f81924d578fc08d6535e304dfcafbf0261d18"
dependencies = [
 "bstr",
 "gix-glob",
 "gix-path",
 "gix-trace",
 "unicode-bom",
]

[[package]]
name = "gix-index"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "855bece2d4153453aa5d0a80d51deea1ce8cd6a3b4cf213da85ac344ccb908a7"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "filetime",
 "fnv",
 "gix-bitmap",
 "gix-features 0.41.1",
 "gix-fs 0.14.0",
 "gix-hash 0.17.0",
 "gix-lock",
 "gix-object",
 "gix-traverse",
 "gix-utils 0.2.0",
 "gix-validate 0.9.4",
 "hashbrown 0.14.5",
 "itoa",
 "libc",
 "memmap2",
 "rustix 0.38.44",
 "smallvec",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-lock"
version = "17.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "570f8b034659f256366dc90f1a24924902f20acccd6a15be96d44d1269e7a796"
dependencies = [
 "gix-tempfile",
 "gix-utils 0.3.0",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-object"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4943fcdae6ffc135920c9ea71e0362ed539182924ab7a85dd9dac8d89b0dd69a"
dependencies = [
 "bstr",
 "gix-actor",
 "gix-date",
 "gix-features 0.41.1",
 "gix-hash 0.17.0",
 "gix-hashtable",
 "gix-path",
 "gix-utils 0.2.0",
 "gix-validate 0.9.4",
 "itoa",
 "smallvec",
 "thiserror 2.0.12",
 "winnow",
]

[[package]]
name = "gix-odb"
version = "0.68.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50306d40dcc982eb6b7593103f066ea6289c7b094cb9db14f3cd2be0b9f5e610"
dependencies = [
 "arc-swap",
 "gix-date",
 "gix-features 0.41.1",
 "gix-fs 0.14.0",
 "gix-hash 0.17.0",
 "gix-hashtable",
 "gix-object",
 "gix-pack",
 "gix-path",
 "gix-quote",
 "parking_lot",
 "tempfile",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-pack"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b65fffb09393c26624ca408d32cfe8776fb94cd0a5cdf984905e1d2f39779cb"
dependencies = [
 "clru",
 "gix-chunk",
 "gix-features 0.41.1",
 "gix-hash 0.17.0",
 "gix-hashtable",
 "gix-object",
 "gix-path",
 "memmap2",
 "smallvec",
 "thiserror 2.0.12",
 "uluru",
]

[[package]]
name = "gix-packetline"
version = "0.18.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "123844a70cf4d5352441dc06bab0da8aef61be94ec239cb631e0ba01dc6d3a04"
dependencies = [
 "bstr",
 "faster-hex 0.9.0",
 "gix-trace",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-packetline-blocking"
version = "0.18.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ecf3ea2e105c7e45587bac04099824301262a6c43357fad5205da36dbb233b3"
dependencies = [
 "bstr",
 "faster-hex 0.9.0",
 "gix-trace",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-path"
version = "0.10.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567f65fec4ef10dfab97ae71f26a27fd4d7fe7b8e3f90c8a58551c41ff3fb65b"
dependencies = [
 "bstr",
 "gix-trace",
 "gix-validate 0.10.0",
 "home",
 "once_cell",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-pathspec"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fef8422c3c9066d649074b24025125963f85232bfad32d6d16aea9453b82ec14"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "gix-attributes",
 "gix-config-value",
 "gix-glob",
 "gix-path",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-protocol"
version = "0.49.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5678ddae1d62880bc30e2200be1b9387af3372e0e88e21f81b4e7f8367355b5a"
dependencies = [
 "bstr",
 "gix-date",
 "gix-features 0.41.1",
 "gix-hash 0.17.0",
 "gix-ref",
 "gix-shallow",
 "gix-transport",
 "gix-utils 0.2.0",
 "maybe-async",
 "thiserror 2.0.12",
 "winnow",
]

[[package]]
name = "gix-quote"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b005c550bf84de3b24aa5e540a23e6146a1c01c7d30470e35d75a12f827f969"
dependencies = [
 "bstr",
 "gix-utils 0.2.0",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-ref"
version = "0.51.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2e1f7eb6b7ce82d2d19961f74bd637bab3ea79b1bc7bfb23dbefc67b0415d8b"
dependencies = [
 "gix-actor",
 "gix-features 0.41.1",
 "gix-fs 0.14.0",
 "gix-hash 0.17.0",
 "gix-lock",
 "gix-object",
 "gix-path",
 "gix-tempfile",
 "gix-utils 0.2.0",
 "gix-validate 0.9.4",
 "memmap2",
 "thiserror 2.0.12",
 "winnow",
]

[[package]]
name = "gix-refspec"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d8587b21e2264a6e8938d940c5c99662779c13a10741a5737b15fc85c252ffc"
dependencies = [
 "bstr",
 "gix-hash 0.17.0",
 "gix-revision",
 "gix-validate 0.9.4",
 "smallvec",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-revision"
version = "0.33.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "342caa4e158df3020cadf62f656307c3948fe4eacfdf67171d7212811860c3e9"
dependencies = [
 "bstr",
 "gix-commitgraph",
 "gix-date",
 "gix-hash 0.17.0",
 "gix-object",
 "gix-revwalk",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-revwalk"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dc7c3d7e5cdc1ab8d35130106e4af0a4f9f9eca0c81f4312b690780e92bde0d"
dependencies = [
 "gix-commitgraph",
 "gix-date",
 "gix-hash 0.17.0",
 "gix-hashtable",
 "gix-object",
 "smallvec",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-sec"
version = "0.10.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47aeb0f13de9ef2f3033f5ff218de30f44db827ac9f1286f9ef050aacddd5888"
dependencies = [
 "bitflags 2.9.0",
 "gix-path",
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "gix-shallow"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc0598aacfe1d52575a21c9492fee086edbb21e228ec36c819c42ab923f434c3"
dependencies = [
 "bstr",
 "gix-hash 0.17.0",
 "gix-lock",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-submodule"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78c7390c2059505c365e9548016d4edc9f35749c6a9112b7b1214400bbc68da2"
dependencies = [
 "bstr",
 "gix-config",
 "gix-path",
 "gix-pathspec",
 "gix-refspec",
 "gix-url",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-tempfile"
version = "17.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c750e8c008453a2dba67a2b0d928b7716e05da31173a3f5e351d5457ad4470aa"
dependencies = [
 "dashmap 6.1.0",
 "gix-fs 0.15.0",
 "libc",
 "once_cell",
 "parking_lot",
 "tempfile",
]

[[package]]
name = "gix-trace"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c396a2036920c69695f760a65e7f2677267ccf483f25046977d87e4cb2665f7"

[[package]]
name = "gix-transport"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3f68c2870bfca8278389d2484a7f2215b67d0b0cc5277d3c72ad72acf41787e"
dependencies = [
 "bstr",
 "gix-command",
 "gix-features 0.41.1",
 "gix-packetline",
 "gix-quote",
 "gix-sec",
 "gix-url",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-traverse"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36c0b049f8bdb61b20016694102f7b507f2e1727e83e9c5e6dad4f7d84ff7384"
dependencies = [
 "bitflags 2.9.0",
 "gix-commitgraph",
 "gix-date",
 "gix-hash 0.17.0",
 "gix-hashtable",
 "gix-object",
 "gix-revwalk",
 "smallvec",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-url"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48dfe23f93f1ddb84977d80bb0dd7aa09d1bf5d5afc0c9b6820cccacc25ae860"
dependencies = [
 "bstr",
 "gix-features 0.41.1",
 "gix-path",
 "percent-encoding",
 "thiserror 2.0.12",
 "url",
]

[[package]]
name = "gix-utils"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "189f8724cf903e7fd57cfe0b7bc209db255cacdcb22c781a022f52c3a774f8d0"
dependencies = [
 "fastrand 2.3.0",
 "unicode-normalization",
]

[[package]]
name = "gix-utils"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5351af2b172caf41a3728eb4455326d84e0d70fe26fc4de74ab0bd37df4191c5"
dependencies = [
 "fastrand 2.3.0",
 "unicode-normalization",
]

[[package]]
name = "gix-validate"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34b5f1253109da6c79ed7cf6e1e38437080bb6d704c76af14c93e2f255234084"
dependencies = [
 "bstr",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-validate"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77b9e00cacde5b51388d28ed746c493b18a6add1f19b5e01d686b3b9ece66d4d"
dependencies = [
 "bstr",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-worktree"
version = "0.40.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7760dbc4b79aa274fed30adc0d41dca6b917641f26e7867c4071b1fb4dc727b"
dependencies = [
 "bstr",
 "gix-attributes",
 "gix-features 0.41.1",
 "gix-fs 0.14.0",
 "gix-glob",
 "gix-hash 0.17.0",
 "gix-ignore",
 "gix-index",
 "gix-object",
 "gix-path",
 "gix-validate 0.9.4",
]

[[package]]
name = "glob"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d1add55171497b4705a648c6b583acafb01d58050a51727785f0b2c8e0a2b2"

[[package]]
name = "globset"
version = "0.4.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54a1028dfc5f5df5da8a56a73e6c153c9a9708ec57232470703592a3f18e49f5"
dependencies = [
 "aho-corasick",
 "bstr",
 "log",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
]

[[package]]
name = "gloo-timers"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbb143cf96099802033e0d4f4963b19fd2e0b728bcf076cd9cf7f6634f092994"
dependencies = [
 "futures-channel",
 "futures-core",
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "glow"
version = "0.14.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d51fa363f025f5c111e03f13eda21162faeacb6911fe8caa0c0349f9cf0c4483"
dependencies = [
 "js-sys",
 "slotmap",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "go_to_line"
version = "0.1.0"
dependencies = [
 "anyhow",
 "editor",
 "gpui",
 "indoc",
 "language",
 "menu",
 "project",
 "rope",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "text",
 "theme",
 "tree-sitter-rust",
 "tree-sitter-typescript",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "google_ai"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "strum 0.27.1",
 "workspace-hack",
]

[[package]]
name = "gpu-alloc"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbcd2dba93594b227a1f57ee09b8b9da8892c34d55aa332e034a228d0fe6a171"
dependencies = [
 "bitflags 2.9.0",
 "gpu-alloc-types",
]

[[package]]
name = "gpu-alloc-ash"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbda7a18a29bc98c2e0de0435c347df935bf59489935d0cbd0b73f1679b6f79a"
dependencies = [
 "ash",
 "gpu-alloc-types",
 "tinyvec",
]

[[package]]
name = "gpu-alloc-types"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98ff03b468aa837d70984d55f5d3f846f6ec31fe34bbb97c4f85219caeee1ca4"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "gpui"
version = "0.1.0"
dependencies = [
 "anyhow",
 "as-raw-xcb-connection",
 "ashpd",
 "async-task",
 "backtrace",
 "bindgen 0.71.1",
 "blade-graphics",
 "blade-macros",
 "blade-util",
 "block",
 "bytemuck",
 "calloop",
 "calloop-wayland-source",
 "cbindgen",
 "cocoa 0.26.0",
 "collections",
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "core-graphics 0.24.0",
 "core-text",
 "core-video",
 "cosmic-text",
 "ctor",
 "derive_more",
 "embed-resource",
 "env_logger 0.11.8",
 "etagere",
 "filedescriptor",
 "flume",
 "font-kit",
 "foreign-types 0.5.0",
 "futures 0.3.31",
 "gpui_macros",
 "http_client",
 "image",
 "inventory",
 "itertools 0.14.0",
 "libc",
 "log",
 "lyon",
 "media",
 "metal",
 "naga",
 "num_cpus",
 "objc",
 "objc2",
 "objc2-metal",
 "oo7",
 "open",
 "parking",
 "parking_lot",
 "pathfinder_geometry",
 "postage",
 "profiling",
 "rand 0.8.5",
 "raw-window-handle",
 "refineable",
 "reqwest_client",
 "resvg",
 "scap",
 "schemars",
 "seahash",
 "semantic_version",
 "serde",
 "serde_derive",
 "serde_json",
 "slotmap",
 "smallvec",
 "smol",
 "strum 0.27.1",
 "sum_tree",
 "taffy",
 "thiserror 2.0.12",
 "unicode-segmentation",
 "usvg",
 "util",
 "uuid",
 "waker-fn",
 "wayland-backend",
 "wayland-client",
 "wayland-cursor",
 "wayland-protocols",
 "wayland-protocols-plasma",
 "windows 0.61.1",
 "windows-core 0.61.0",
 "windows-numerics",
 "windows-registry 0.5.1",
 "workspace-hack",
 "x11-clipboard",
 "x11rb",
 "xim",
 "xkbcommon",
]

[[package]]
name = "gpui_macros"
version = "0.1.0"
dependencies = [
 "gpui",
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "workspace-hack",
]

[[package]]
name = "gpui_tokio"
version = "0.1.0"
dependencies = [
 "gpui",
 "tokio",
 "util",
 "workspace-hack",
]

[[package]]
name = "grid"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d196ffc1627db18a531359249b2bf8416178d84b729f3cebeb278f285fb9b58c"

[[package]]
name = "group"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5dfbfb3a6cfbd390d5c9564ab283a0349b9b9fcd46a706c1eb10e0db70bfbac7"
dependencies = [
 "ff",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "h2"
version = "0.3.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81fe527a889e1532da5c525686d96d4c2e74cdd345badf8dfef9f6b39dd5f5e8"
dependencies = [
 "bytes 1.10.1",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http 0.2.12",
 "indexmap",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "h2"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75249d144030531f8dee69fe9cea04d3edf809a017ae445e2abdff6629e86633"
dependencies = [
 "atomic-waker",
 "bytes 1.10.1",
 "fnv",
 "futures-core",
 "futures-sink",
 "http 1.3.1",
 "indexmap",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "half"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "459196ed295495a68f7d7fe1d84f6c4b7ff0e21fe3017b2f283c6fac3ad803c9"
dependencies = [
 "cfg-if",
 "crunchy",
 "num-traits",
]

[[package]]
name = "handlebars"
version = "4.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faa67bab9ff362228eb3d00bd024a4965d8231bbb7921167f0cfa66c6626b225"
dependencies = [
 "log",
 "pest",
 "pest_derive",
 "rust-embed",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "handlebars"
version = "6.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "759e2d5aea3287cb1190c8ec394f42866cb5bf74fcbf213f354e3c856ea26098"
dependencies = [
 "derive_builder",
 "log",
 "num-order",
 "pest",
 "pest_derive",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "hash32"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47d60b12902ba28e2730cd37e95b8c9223af2808df9e902d4df49588d1470606"
dependencies = [
 "byteorder",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"
dependencies = [
 "ahash 0.7.8",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash 0.8.11",
 "allocator-api2",
]

[[package]]
name = "hashbrown"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84b26c544d002229e640969970a2e74021aadf6e2f96372b9c58eff97de08eb3"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
 "serde",
]

[[package]]
name = "hashlink"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8094feaf31ff591f651a2664fb9cfd92bba7a60ce3197265e9482ebe753c8f7"
dependencies = [
 "hashbrown 0.14.5",
]

[[package]]
name = "hashlink"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7382cf6263419f2d8df38c55d7da83da5c18aef87fc7a7fc1fb1e344edfe14c1"
dependencies = [
 "hashbrown 0.15.3",
]

[[package]]
name = "headers"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06683b93020a07e3dbcf5f8c0f6d40080d725bea7936fc01ad345c01b97dc270"
dependencies = [
 "base64 0.21.7",
 "bytes 1.10.1",
 "headers-core",
 "http 0.2.12",
 "httpdate",
 "mime",
 "sha1",
]

[[package]]
name = "headers-core"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7f66481bfee273957b1f20485a4ff3362987f85b2c236580d81b4eb7a326429"
dependencies = [
 "http 0.2.12",
]

[[package]]
name = "heapless"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bfb9eb618601c89945a70e254898da93b13be0388091d42117462b265bb3fad"
dependencies = [
 "hash32",
 "stable_deref_trait",
]

[[package]]
name = "heck"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d621efb26863f0e9924c6ac577e8275e5e6b77455db64ffa6c65c904e9e132c"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "heed"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd54745cfacb7b97dee45e8fdb91814b62bccddb481debb7de0f9ee6b7bf5b43"
dependencies = [
 "bitflags 2.9.0",
 "byteorder",
 "heed-traits",
 "heed-types",
 "libc",
 "lmdb-master-sys",
 "once_cell",
 "page_size",
 "serde",
 "synchronoise",
 "url",
]

[[package]]
name = "heed-traits"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb3130048d404c57ce5a1ac61a903696e8fcde7e8c2991e9fcfc1f27c3ef74ff"

[[package]]
name = "heed-types"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c255bdf46e07fb840d120a36dcc81f385140d7191c76a7391672675c01a55d"
dependencies = [
 "bincode",
 "byteorder",
 "heed-traits",
 "serde",
 "serde_json",
]

[[package]]
name = "hermit-abi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d231dfb89cfffdbc30e7fc41579ed6066ad03abda9e567ccafae602b97ec5024"

[[package]]
name = "hermit-abi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbf6a919d6cf397374f7dfeeea91d974c7c0a7221d0d0f4f20d859d329e53fcc"

[[package]]
name = "hermit-abi"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbd780fe5cc30f81464441920d82ac8740e2e46b29a6fad543ddd075229ce37e"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"

[[package]]
name = "hexf-parse"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfa686283ad6dd069f105e5ab091b04c62850d3e4cf5d67debad1933f55023df"

[[package]]
name = "hidden-trait"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68ed9e850438ac849bec07e7d09fbe9309cbd396a5988c30b010580ce08860df"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "hkdf"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5f8eb2ad728638ea2c7d47a21db23b7b58a72ed6a38256b8a1849f15fbbdf7"
dependencies = [
 "hmac",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest",
]

[[package]]
name = "home"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589533453244b0995c858700322199b2becb13b627df2851f64a2775d024abcf"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "hound"
version = "3.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62adaabb884c94955b19907d60019f4e145d091c75345379e70d1ee696f7854f"

[[package]]
name = "html5ever"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c13771afe0e6e846f1e67d038d4cb29998a6779f93c809212e4e9c32efd244d4"
dependencies = [
 "log",
 "mac",
 "markup5ever 0.12.1",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "html5ever"
version = "0.31.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "953cbbe631aae7fc0a112702ad5d3aaf09da38beaf45ea84610d6e1c358f569c"
dependencies = [
 "log",
 "mac",
 "markup5ever 0.16.1",
 "match_token",
]

[[package]]
name = "html_to_markdown"
version = "0.1.0"
dependencies = [
 "anyhow",
 "html5ever 0.27.0",
 "indoc",
 "markup5ever_rcdom",
 "pretty_assertions",
 "regex",
 "workspace-hack",
]

[[package]]
name = "http"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "601cbb57e577e2f5ef5be8e7b83f0f63994f25aa94d673e54a92d5c516d101f1"
dependencies = [
 "bytes 1.10.1",
 "fnv",
 "itoa",
]

[[package]]
name = "http"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4a85d31aea989eead29a3aaf9e1115a180df8282431156e533de47660892565"
dependencies = [
 "bytes 1.10.1",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ceab25649e9960c0311ea418d17bee82c0dcec1bd053b5f9a66e265a693bed2"
dependencies = [
 "bytes 1.10.1",
 "http 0.2.12",
 "pin-project-lite",
]

[[package]]
name = "http-body"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1efedce1fb8e6913f23e0c92de8e62cd5b772a67e7b3946df930a62566c93184"
dependencies = [
 "bytes 1.10.1",
 "http 1.3.1",
]

[[package]]
name = "http-body-util"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b021d93e26becf5dc7e1b75b1bed1fd93124b374ceb73f43d4d4eafec896a64a"
dependencies = [
 "bytes 1.10.1",
 "futures-core",
 "http 1.3.1",
 "http-body 1.0.1",
 "pin-project-lite",
]

[[package]]
name = "http-range-header"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "add0ab9360ddbd88cfeb3bd9574a1d85cfdfa14db10b3e21d3700dbc4328758f"

[[package]]
name = "http-types"
version = "2.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e9b187a72d63adbfba487f48095306ac823049cb504ee195541e91c7775f5ad"
dependencies = [
 "anyhow",
 "async-channel 1.9.0",
 "base64 0.13.1",
 "futures-lite 1.13.0",
 "http 0.2.12",
 "infer",
 "pin-project-lite",
 "rand 0.7.3",
 "serde",
 "serde_json",
 "serde_qs 0.8.5",
 "serde_urlencoded",
 "url",
]

[[package]]
name = "http_client"
version = "0.1.0"
dependencies = [
 "anyhow",
 "bytes 1.10.1",
 "derive_more",
 "futures 0.3.31",
 "http 1.3.1",
 "log",
 "serde",
 "serde_json",
 "url",
 "workspace-hack",
]

[[package]]
name = "http_client_tls"
version = "0.1.0"
dependencies = [
 "rustls 0.23.26",
 "rustls-platform-verifier",
 "workspace-hack",
]

[[package]]
name = "httparse"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dbf3de79e51f3d586ab4cb9d5c3e2c14aa28ed23d180cf89b4df0454a69cc87"

[[package]]
name = "httpdate"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df3b46402a9d5adb4c86a0cf463f42e19994e3ee891101b1841f30a545cb49a9"

[[package]]
name = "human_bytes"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91f255a4535024abf7640cb288260811fc14794f62b063652ed349f9a6c2348e"

[[package]]
name = "humantime"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b112acc8b3adf4b107a8ec20977da0273a8c386765a3ec0229bd500a1443f9f"

[[package]]
name = "hyper"
version = "0.14.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41dfc780fdec9373c01bae43289ea34c972e40ee3c9f6b3c8801a35f35586ce7"
dependencies = [
 "bytes 1.10.1",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2 0.3.26",
 "http 0.2.12",
 "http-body 0.4.6",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc2b571658e38e0c01b1fdca3bbbe93c00d3d71693ff2770043f8c29bc7d6f80"
dependencies = [
 "bytes 1.10.1",
 "futures-channel",
 "futures-util",
 "h2 0.4.9",
 "http 1.3.1",
 "http-body 1.0.1",
 "httparse",
 "itoa",
 "pin-project-lite",
 "smallvec",
 "tokio",
 "want",
]

[[package]]
name = "hyper-rustls"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3efd23720e2049821a693cbc7e65ea87c72f1c58ff2f9522ff332b1491e590"
dependencies = [
 "futures-util",
 "http 0.2.12",
 "hyper 0.14.32",
 "log",
 "rustls 0.21.12",
 "rustls-native-certs 0.6.3",
 "tokio",
 "tokio-rustls 0.24.1",
]

[[package]]
name = "hyper-rustls"
version = "0.27.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d191583f3da1305256f22463b9bb0471acad48a4e534a5218b9963e9c1f59b2"
dependencies = [
 "futures-util",
 "http 1.3.1",
 "hyper 1.6.0",
 "hyper-util",
 "rustls 0.23.26",
 "rustls-native-certs 0.8.1",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls 0.26.2",
 "tower-service",
]

[[package]]
name = "hyper-tls"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6183ddfa99b85da61a140bea0efc93fdf56ceaa041b37d553518030827f9905"
dependencies = [
 "bytes 1.10.1",
 "hyper 0.14.32",
 "native-tls",
 "tokio",
 "tokio-native-tls",
]

[[package]]
name = "hyper-util"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "497bbc33a26fdd4af9ed9c70d63f61cf56a938375fbb32df34db9b1cd6d643f2"
dependencies = [
 "bytes 1.10.1",
 "futures-channel",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "hyper 1.6.0",
 "libc",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
]

[[package]]
name = "iana-time-zone"
version = "0.1.63"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0c919e5debc312ad217002b8048a17b7d83f80703865bbfcfebb0458b0b27d8"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "log",
 "wasm-bindgen",
 "windows-core 0.61.0",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "icons"
version = "0.1.0"
dependencies = [
 "serde",
 "strum 0.27.1",
 "workspace-hack",
]

[[package]]
name = "icu_collections"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fa452206ebee18c4b5c2274dbf1de17008e874b4dc4f0aea9d01ca79e4526"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locid"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13acbb8371917fc971be86fc8057c41a64b521c184808a698c02acc242dbf637"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_locid_transform"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01d11ac35de8e40fdeda00d9e1e9d92525f3f9d887cdd7aa81d727596788b54e"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_locid_transform_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_locid_transform_data"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7515e6d781098bf9f7205ab3fc7e9709d34554ae0b21ddbcb5febfa4bc7df11d"

[[package]]
name = "icu_normalizer"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19ce3e0da2ec68599d193c93d088142efd7f9c5d6fc9b803774855747dc6a84f"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "utf16_iter",
 "utf8_iter",
 "write16",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5e8338228bdc8ab83303f16b797e177953730f601a96c25d10cb3ab0daa0cb7"

[[package]]
name = "icu_properties"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93d6020766cfc6302c15dbbc9c8778c37e62c14427cb7f6e601d849e092aeef5"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locid_transform",
 "icu_properties_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85fb8799753b75aee8d2a21d7c14d9f38921b54b3dbda10f5a3c7a7b82dba5e2"

[[package]]
name = "icu_provider"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ed421c8a8ef78d3e2dbc98a973be2f3770cb42b606e3ab18d6237c4dfde68d9"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_provider_macros",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_provider_macros"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ec89e9337638ecdc08744df490b221a7399bf8d164eb52a665454e60e075ad6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "id-arena"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25a2bc672d1148e28034f176e01fffebb08b35768468cc954630da77a1449005"

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daca1df1c957320b2cf139ac61e7bd64fed304c5040df000a745aa1de3b4ef71"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "ignore"
version = "0.4.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d89fd380afde86567dfba715db065673989d6253f42b88179abd3eae47bda4b"
dependencies = [
 "crossbeam-deque",
 "globset",
 "log",
 "memchr",
 "regex-automata 0.4.9",
 "same-file",
 "walkdir",
 "winapi-util",
]

[[package]]
name = "image"
version = "0.25.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db35664ce6b9810857a38a906215e75a9c879f0696556a39f59c62829710251a"
dependencies = [
 "bytemuck",
 "byteorder-lite",
 "color_quant",
 "exr",
 "gif",
 "image-webp",
 "num-traits",
 "png",
 "qoi",
 "ravif",
 "rayon",
 "rgb",
 "tiff",
 "zune-core",
 "zune-jpeg",
]

[[package]]
name = "image-webp"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b77d01e822461baa8409e156015a1d91735549f0f2c17691bd2d996bef238f7f"
dependencies = [
 "byteorder-lite",
 "quick-error",
]

[[package]]
name = "image_viewer"
version = "0.1.0"
dependencies = [
 "anyhow",
 "db",
 "editor",
 "file_icons",
 "gpui",
 "language",
 "log",
 "project",
 "schemars",
 "serde",
 "settings",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "imagesize"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edcd27d72f2f071c64249075f42e205ff93c9a4c5f6c6da53e79ed9f9832c285"

[[package]]
name = "imara-diff"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17d34b7d42178945f775e84bc4c36dde7c1c6cdfea656d3354d009056f2bb3d2"
dependencies = [
 "hashbrown 0.15.3",
]

[[package]]
name = "imgref"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0263a3d970d5c054ed9312c0057b4f3bde9c0b33836d3637361d4a9e6e7a408"

[[package]]
name = "indexed_docs"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "cargo_metadata",
 "collections",
 "derive_more",
 "extension",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "heed",
 "html_to_markdown",
 "http_client",
 "indexmap",
 "indoc",
 "parking_lot",
 "paths",
 "pretty_assertions",
 "serde",
 "strum 0.27.1",
 "util",
 "workspace-hack",
]

[[package]]
name = "indexmap"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cea70ddb795996207ad57735b50c5982d8844f38ba9ee5f1aedcfb708a2aa11e"
dependencies = [
 "equivalent",
 "hashbrown 0.15.3",
 "serde",
]

[[package]]
name = "indoc"
version = "2.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4c7245a08504955605670dbf141fceab975f15ca21570696aebe9d2e71576bd"

[[package]]
name = "infer"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64e9829a50b42bb782c1df523f78d332fe371b10c661e78b7a3c34b0198e9fac"

[[package]]
name = "inherent"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c38228f24186d9cc68c729accb4d413be9eaed6ad07ff79e0270d9e56f3de13"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "inline_completion"
version = "0.1.0"
dependencies = [
 "anyhow",
 "gpui",
 "language",
 "project",
 "workspace-hack",
 "zed_llm_client",
]

[[package]]
name = "inline_completion_button"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "copilot",
 "editor",
 "feature_flags",
 "fs",
 "futures 0.3.31",
 "gpui",
 "indoc",
 "inline_completion",
 "language",
 "lsp",
 "paths",
 "project",
 "regex",
 "serde_json",
 "settings",
 "supermaven",
 "telemetry",
 "theme",
 "ui",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zed_llm_client",
 "zeta",
]

[[package]]
name = "inotify"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f37dccff2791ab604f9babef0ba14fbe0be30bd368dc541e2b08d07c8aa908f3"
dependencies = [
 "bitflags 2.9.0",
 "inotify-sys",
 "libc",
]

[[package]]
name = "inotify-sys"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e05c02b5e89bff3b946cedeca278abc628fe811e604f027c45a8aa3cf793d0eb"
dependencies = [
 "libc",
]

[[package]]
name = "inout"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "879f10e63c20629ecabbb64a8010319738c66a5cd0c29b02d63d272b03751d01"
dependencies = [
 "block-padding",
 "generic-array",
]

[[package]]
name = "inspector_ui"
version = "0.1.0"
dependencies = [
 "anyhow",
 "command_palette_hooks",
 "editor",
 "fuzzy",
 "gpui",
 "language",
 "project",
 "serde_json",
 "serde_json_lenient",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "install_cli"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "gpui",
 "release_channel",
 "smol",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "instant"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0242819d153cba4b4b05a5a8f2a7e9bbf97b6055b2a002b395c96b5ff3c0222"
dependencies = [
 "cfg-if",
]

[[package]]
name = "interim"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9ce9099a85f468663d3225bf87e85d0548968441e1db12248b996b24f0f5b5a"
dependencies = [
 "chrono",
 "logos",
]

[[package]]
name = "interpolate_name"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c34819042dc3d3971c46c2190835914dfbe0c3c13f61449b2997f4e9722dfa60"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "inventory"
version = "0.3.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab08d7cd2c5897f2c949e5383ea7c7db03fb19130ffcfbf7eda795137ae3cb83"
dependencies = [
 "rustversion",
]

[[package]]
name = "io-extras"
version = "0.18.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2285ddfe3054097ef4b2fe909ef8c3bcd1ea52a8f0d274416caebeef39f04a65"
dependencies = [
 "io-lifetimes",
 "windows-sys 0.59.0",
]

[[package]]
name = "io-lifetimes"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06432fb54d3be7964ecd3649233cddf80db2832f47fec34c01f65b3d9d774983"

[[package]]
name = "io-surface"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8283575d5f0b2e7447ec0840363879d71c0fa325d4c699d5b45208ea4a51f45e"
dependencies = [
 "cgl",
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "leaky-cow",
 "libc",
]

[[package]]
name = "iovec"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2b3ea6ff95e175473f8ffe6a7eb7c00d054240321b84c57051175fe3c1e075e"
dependencies = [
 "libc",
]

[[package]]
name = "ipc-channel"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fb8251fb7bcd9ccd3725ed8deae9fe7db8e586495c9eb5b0c52e6233e5e75ea"
dependencies = [
 "bincode",
 "crossbeam-channel",
 "fnv",
 "lazy_static",
 "libc",
 "mio",
 "rand 0.8.5",
 "serde",
 "tempfile",
 "uuid",
 "windows 0.58.0",
]

[[package]]
name = "ipnet"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "469fb0b9cefa57e3ef31275ee7cacb78f2fdca44e4765491884a2b119d4eb130"

[[package]]
name = "is-docker"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "928bae27f42bc99b60d9ac7334e3a21d10ad8f1835a4e12ec3ec0464765ed1b3"
dependencies = [
 "once_cell",
]

[[package]]
name = "is-terminal"
version = "0.4.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e04d7f318608d35d4b61ddd75cbdaee86b023ebe2bd5a66ee0915f0bf93095a9"
dependencies = [
 "hermit-abi 0.5.0",
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "is-wsl"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "173609498df190136aa7dea1a91db051746d339e18476eed5ca40521f02d7aa5"
dependencies = [
 "is-docker",
 "once_cell",
]

[[package]]
name = "is_terminal_polyfill"
version = "1.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7943c866cc5cd64cbc25b2e01621d07fa8eb2a1a23160ee81ce38704e97b8ecf"

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1c173a5686ce8bfa551b3563d0c2170bf24ca44da99c7ca4bfdab5418c3fe57"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413ee7dfc52ee1a4949ceeb7dbc8a33f2d6c088194d9f922fb8318faf1f01186"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b192c782037fadd9cfa75548310488aabdbf3d2da73885b31bd0abd03351285"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a5f13b858c8d314ee3e8f639011f7ccefe71f97f96e50151fb991f267928e2c"

[[package]]
name = "jiff"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a064218214dc6a10fbae5ec5fa888d80c45d611aba169222fc272072bf7aef6"
dependencies = [
 "jiff-static",
 "jiff-tzdb-platform",
 "log",
 "portable-atomic",
 "portable-atomic-util",
 "serde",
 "windows-sys 0.59.0",
]

[[package]]
name = "jiff-static"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "199b7932d97e325aff3a7030e141eafe7f2c6268e1d1b24859b753a627f45254"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "jiff-tzdb"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1283705eb0a21404d2bfd6eef2a7593d240bc42a0bdb39db0ad6fa2ec026524"

[[package]]
name = "jiff-tzdb-platform"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "875a5a69ac2bab1a891711cf5eccbec1ce0341ea805560dcd90b7a2e925132e8"
dependencies = [
 "jiff-tzdb",
]

[[package]]
name = "jj"
version = "0.1.0"
dependencies = [
 "anyhow",
 "gpui",
 "jj-lib",
 "workspace-hack",
]

[[package]]
name = "jj-lib"
version = "0.29.0"
source = "git+https://github.com/jj-vcs/jj?rev=e18eb8e05efaa153fad5ef46576af145bba1807f#e18eb8e05efaa153fad5ef46576af145bba1807f"
dependencies = [
 "async-trait",
 "blake2",
 "bstr",
 "chrono",
 "clru",
 "digest",
 "dunce",
 "either",
 "futures 0.3.31",
 "gix",
 "glob",
 "hashbrown 0.15.3",
 "hex",
 "ignore",
 "indexmap",
 "interim",
 "itertools 0.14.0",
 "jj-lib-proc-macros",
 "maplit",
 "once_cell",
 "pest",
 "pest_derive",
 "pollster 0.4.0",
 "prost 0.13.5",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rayon",
 "ref-cast",
 "regex",
 "rustix 1.0.7",
 "same-file",
 "serde",
 "serde_json",
 "smallvec",
 "strsim",
 "tempfile",
 "thiserror 2.0.12",
 "toml_edit",
 "tracing",
 "version_check",
 "winreg 0.52.0",
]

[[package]]
name = "jj-lib-proc-macros"
version = "0.29.0"
source = "git+https://github.com/jj-vcs/jj?rev=e18eb8e05efaa153fad5ef46576af145bba1807f#e18eb8e05efaa153fad5ef46576af145bba1807f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "jj_ui"
version = "0.1.0"
dependencies = [
 "command_palette_hooks",
 "feature_flags",
 "fuzzy",
 "gpui",
 "jj",
 "picker",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "jni"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a87aa2bb7d2af34197c04845522473242e1aa17c12f4935d5856491a7fb8c97"
dependencies = [
 "cesu8",
 "cfg-if",
 "combine",
 "jni-sys",
 "log",
 "thiserror 1.0.69",
 "walkdir",
 "windows-sys 0.45.0",
]

[[package]]
name = "jni-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8eaf4bc02d17cbdd7ff4c7438cafcdf7fb9a4613313ad11b4f8fefe7d3fa0130"

[[package]]
name = "jobserver"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38f262f097c174adebe41eb73d66ae9c06b2844fb0da69969647bbddd9b0538a"
dependencies = [
 "getrandom 0.3.2",
 "libc",
]

[[package]]
name = "journal"
version = "0.1.0"
dependencies = [
 "anyhow",
 "chrono",
 "editor",
 "gpui",
 "log",
 "schemars",
 "serde",
 "settings",
 "shellexpand 2.1.2",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "jpeg-decoder"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5d4a7da358eff58addd2877a45865158f0d78c911d43a5784ceb7bbf52833b0"

[[package]]
name = "js-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cfaf33c695fc6e08064efbc1f72ec937429614f25eef83af942d0e227c3a28f"
dependencies = [
 "once_cell",
 "wasm-bindgen",
]

[[package]]
name = "json_dotpath"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbdcfef3cf5591f0cef62da413ae795e3d1f5a00936ccec0b2071499a32efd1a"
dependencies = [
 "serde",
 "serde_derive",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "jsonschema"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1b46a0365a611fbf1d2143104dcf910aada96fafd295bab16c60b802bf6fa1d"
dependencies = [
 "ahash 0.8.11",
 "base64 0.22.1",
 "bytecount",
 "email_address",
 "fancy-regex 0.14.0",
 "fraction",
 "idna",
 "itoa",
 "num-cmp",
 "num-traits",
 "once_cell",
 "percent-encoding",
 "referencing",
 "regex",
 "regex-syntax 0.8.5",
 "reqwest 0.12.15 (registry+https://github.com/rust-lang/crates.io-index)",
 "serde",
 "serde_json",
 "uuid-simd",
]

[[package]]
name = "jsonwebtoken"
version = "9.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a87cc7a48537badeae96744432de36f4be2b4a34a05a5ef32e9dd8a1c169dde"
dependencies = [
 "base64 0.22.1",
 "js-sys",
 "pem",
 "ring",
 "serde",
 "serde_json",
 "simple_asn1",
]

[[package]]
name = "jupyter-protocol"
version = "0.6.0"
source = "git+https://github.com/ConradIrwin/runtimed?rev=7130c804216b6914355d15d0b91ea91f6babd734#7130c804216b6914355d15d0b91ea91f6babd734"
dependencies = [
 "anyhow",
 "async-trait",
 "bytes 1.10.1",
 "chrono",
 "futures 0.3.31",
 "serde",
 "serde_json",
 "uuid",
]

[[package]]
name = "jupyter-websocket-client"
version = "0.9.0"
source = "git+https://github.com/ConradIrwin/runtimed?rev=7130c804216b6914355d15d0b91ea91f6babd734#7130c804216b6914355d15d0b91ea91f6babd734"
dependencies = [
 "anyhow",
 "async-trait",
 "async-tungstenite",
 "futures 0.3.31",
 "jupyter-protocol",
 "serde",
 "serde_json",
 "url",
 "uuid",
]

[[package]]
name = "khronos-egl"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6aae1df220ece3c0ada96b8153459b67eebe9ae9212258bb0134ae60416fdf76"
dependencies = [
 "libc",
 "libloading",
]

[[package]]
name = "kqueue"
version = "1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7447f1ca1b7b563588a205fe93dea8df60fd981423a768bc1c0ded35ed147d0c"
dependencies = [
 "kqueue-sys",
 "libc",
]

[[package]]
name = "kqueue-sys"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed9625ffda8729b85e45cf04090035ac368927b8cebc34898e7c120f52e4838b"
dependencies = [
 "bitflags 1.3.2",
 "libc",
]

[[package]]
name = "kstring"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "558bf9508a558512042d3095138b1f7b8fe90c5467d94f9f1da28b3731c5dbd1"
dependencies = [
 "static_assertions",
]

[[package]]
name = "kurbo"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89234b2cc610a7dd927ebde6b41dd1a5d4214cffaef4cf1fb2195d592f92518f"
dependencies = [
 "arrayvec",
 "smallvec",
]

[[package]]
name = "kv-log-macro"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0de8b303297635ad57c9f5059fd9cee7a47f8e8daa09df0fcd07dd39fb22977f"
dependencies = [
 "log",
]

[[package]]
name = "language"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "clock",
 "collections",
 "ctor",
 "diffy",
 "ec4rs",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "globset",
 "gpui",
 "http_client",
 "imara-diff",
 "indoc",
 "itertools 0.14.0",
 "log",
 "lsp",
 "parking_lot",
 "postage",
 "pretty_assertions",
 "rand 0.8.5",
 "regex",
 "rpc",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "shellexpand 2.1.2",
 "smallvec",
 "smol",
 "streaming-iterator",
 "strsim",
 "sum_tree",
 "task",
 "text",
 "theme",
 "tree-sitter",
 "tree-sitter-elixir",
 "tree-sitter-embedded-template",
 "tree-sitter-heex",
 "tree-sitter-html",
 "tree-sitter-json",
 "tree-sitter-md",
 "tree-sitter-python",
 "tree-sitter-ruby",
 "tree-sitter-rust",
 "tree-sitter-typescript",
 "unicase",
 "unindent",
 "util",
 "watch",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "language_extension"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "collections",
 "extension",
 "fs",
 "futures 0.3.31",
 "gpui",
 "language",
 "lsp",
 "serde",
 "serde_json",
 "util",
 "workspace-hack",
]

[[package]]
name = "language_model"
version = "0.1.0"
dependencies = [
 "anthropic",
 "anyhow",
 "base64 0.22.1",
 "client",
 "collections",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "icons",
 "image",
 "parking_lot",
 "proto",
 "schemars",
 "serde",
 "serde_json",
 "smol",
 "telemetry_events",
 "thiserror 2.0.12",
 "util",
 "workspace-hack",
 "zed_llm_client",
]

[[package]]
name = "language_models"
version = "0.1.0"
dependencies = [
 "anthropic",
 "anyhow",
 "aws-config",
 "aws-credential-types",
 "aws_http_client",
 "bedrock",
 "client",
 "collections",
 "copilot",
 "credentials_provider",
 "deepseek",
 "editor",
 "fs",
 "futures 0.3.31",
 "google_ai",
 "gpui",
 "gpui_tokio",
 "http_client",
 "language_model",
 "lmstudio",
 "log",
 "menu",
 "mistral",
 "ollama",
 "open_ai",
 "open_router",
 "partial-json-fixer",
 "project",
 "proto",
 "release_channel",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "strum 0.27.1",
 "theme",
 "thiserror 2.0.12",
 "tiktoken-rs",
 "tokio",
 "ui",
 "util",
 "workspace-hack",
 "zed_llm_client",
]

[[package]]
name = "language_selector"
version = "0.1.0"
dependencies = [
 "anyhow",
 "editor",
 "file_finder",
 "file_icons",
 "fuzzy",
 "gpui",
 "language",
 "picker",
 "project",
 "settings",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "language_tools"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "collections",
 "copilot",
 "editor",
 "futures 0.3.31",
 "gpui",
 "itertools 0.14.0",
 "language",
 "lsp",
 "project",
 "release_channel",
 "serde_json",
 "settings",
 "theme",
 "tree-sitter",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zlog",
]

[[package]]
name = "languages"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-compression",
 "async-tar",
 "async-trait",
 "chrono",
 "collections",
 "dap",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "language",
 "log",
 "lsp",
 "node_runtime",
 "parking_lot",
 "paths",
 "pet",
 "pet-conda",
 "pet-core",
 "pet-fs",
 "pet-poetry",
 "pet-reporter",
 "pretty_assertions",
 "project",
 "regex",
 "rope",
 "rust-embed",
 "schemars",
 "serde",
 "serde_json",
 "serde_json_lenient",
 "settings",
 "smol",
 "snippet_provider",
 "task",
 "text",
 "theme",
 "toml 0.8.20",
 "tree-sitter",
 "tree-sitter-bash",
 "tree-sitter-c",
 "tree-sitter-cpp",
 "tree-sitter-css",
 "tree-sitter-diff",
 "tree-sitter-gitcommit",
 "tree-sitter-go",
 "tree-sitter-gomod",
 "tree-sitter-gowork",
 "tree-sitter-jsdoc",
 "tree-sitter-json",
 "tree-sitter-md",
 "tree-sitter-python",
 "tree-sitter-regex",
 "tree-sitter-rust",
 "tree-sitter-typescript",
 "tree-sitter-yaml",
 "unindent",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "lazy_static"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbd2bcb4c963f2ddae06a2efc7e9f3591312473c50c6685e1f298068316e66fe"
dependencies = [
 "spin",
]

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "leak"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd100e01f1154f2908dfa7d02219aeab25d0b9c7fa955164192e3245255a0c73"

[[package]]
name = "leaky-cow"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40a8225d44241fd324a8af2806ba635fc7c8a7e9a7de4d5cf3ef54e71f5926fc"
dependencies = [
 "leak",
]

[[package]]
name = "leb128"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "884e2677b40cc8c339eaefcb701c32ef1fd2493d71118dc0ca4b6a736c93bd67"

[[package]]
name = "leb128fmt"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09edd9e8b54e49e587e4f6295a7d29c3ea94d469cb40ab8ca70b288248a81db2"

[[package]]
name = "lebe"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03087c2bad5e1034e8cace5926dec053fb3790248370865f5117a7d0213354c8"

[[package]]
name = "libc"
version = "0.2.172"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d750af042f7ef4f724306de029d18836c26c1765a54a6a3f094cbd23a7267ffa"

[[package]]
name = "libdbus-sys"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06085512b750d640299b79be4bad3d2fa90a9c00b1fd9e1b46364f66f0485c72"
dependencies = [
 "cc",
 "pkg-config",
]

[[package]]
name = "libfuzzer-sys"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf78f52d400cf2d84a3a973a78a592b4adc535739e0a5597a0da6f0c357adc75"
dependencies = [
 "arbitrary",
 "cc",
]

[[package]]
name = "libgit2-sys"
version = "0.18.1+1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1dcb20f84ffcdd825c7a311ae347cce604a6f084a767dec4a4929829645290e"
dependencies = [
 "cc",
 "libc",
 "libz-sys",
 "pkg-config",
]

[[package]]
name = "libloading"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc2f4eb4bc735547cfed7c0a4922cbd04a4655978c09b54f1f7b228750664c34"
dependencies = [
 "cfg-if",
 "windows-targets 0.52.6",
]

[[package]]
name = "libm"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8355be11b20d696c8f18f6cc018c4e372165b1fa8126cef092399c9951984ffa"

[[package]]
name = "libmimalloc-sys"
version = "0.1.42"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec9d6fac27761dabcd4ee73571cdb06b7022dc99089acbe5435691edffaac0f4"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "libredox"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0ff37bd590ca25063e35af745c343cb7a0271906fb7b37e4813e8f79f00268d"
dependencies = [
 "bitflags 2.9.0",
 "libc",
 "redox_syscall 0.5.11",
]

[[package]]
name = "libsqlite3-sys"
version = "0.30.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e99fb7a497b1e3339bc746195567ed8d3e24945ecd636e3619d20b9de9e9149"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "libwebrtc"
version = "0.3.10"
source = "git+https://github.com/zed-industries/livekit-rust-sdks?rev=80bb8f4c9112789f7c24cc98d8423010977806a6#80bb8f4c9112789f7c24cc98d8423010977806a6"
dependencies = [
 "cxx",
 "jni",
 "js-sys",
 "lazy_static",
 "livekit-protocol",
 "livekit-runtime",
 "log",
 "parking_lot",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "webrtc-sys",
]

[[package]]
name = "libz-rs-sys"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6489ca9bd760fe9642d7644e827b0c9add07df89857b0416ee15c1cc1a3b8c5a"
dependencies = [
 "zlib-rs",
]

[[package]]
name = "libz-sys"
version = "1.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b70e7a7df205e92a1a4cd9aaae7898dac0aa555503cc0a649494d0d60e7651d"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "link-cplusplus"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a6f6da007f968f9def0d65a05b187e2960183de70c160204ecfccf0ee330212"
dependencies = [
 "cc",
]

[[package]]
name = "linkify"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1dfa36d52c581e9ec783a7ce2a5e0143da6237be5811a0b3153fedfdbe9f780"
dependencies = [
 "memchr",
]

[[package]]
name = "linux-raw-sys"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d26c52dbd32dccf2d10cac7725f8eae5296885fb5703b261f7d0a0739ec807ab"

[[package]]
name = "linux-raw-sys"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd945864f07fe9f5371a27ad7b52a172b4b499999f1d97574c9fa68373937e12"

[[package]]
name = "litemap"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23fb14cb19457329c82206317a5663005a4d404783dc74f4252769b0d5f42856"

[[package]]
name = "livekit"
version = "0.7.8"
source = "git+https://github.com/zed-industries/livekit-rust-sdks?rev=80bb8f4c9112789f7c24cc98d8423010977806a6#80bb8f4c9112789f7c24cc98d8423010977806a6"
dependencies = [
 "chrono",
 "futures-util",
 "lazy_static",
 "libloading",
 "libwebrtc",
 "livekit-api",
 "livekit-protocol",
 "livekit-runtime",
 "log",
 "parking_lot",
 "prost 0.12.6",
 "semver",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "livekit-api"
version = "0.4.2"
source = "git+https://github.com/zed-industries/livekit-rust-sdks?rev=80bb8f4c9112789f7c24cc98d8423010977806a6#80bb8f4c9112789f7c24cc98d8423010977806a6"
dependencies = [
 "futures-util",
 "http 0.2.12",
 "livekit-protocol",
 "livekit-runtime",
 "log",
 "parking_lot",
 "pbjson-types",
 "prost 0.12.6",
 "rand 0.9.1",
 "reqwest 0.11.27",
 "scopeguard",
 "serde",
 "sha2",
 "thiserror 1.0.69",
 "tokio",
 "tokio-tungstenite 0.26.2",
 "url",
]

[[package]]
name = "livekit-protocol"
version = "0.3.9"
source = "git+https://github.com/zed-industries/livekit-rust-sdks?rev=80bb8f4c9112789f7c24cc98d8423010977806a6#80bb8f4c9112789f7c24cc98d8423010977806a6"
dependencies = [
 "futures-util",
 "livekit-runtime",
 "parking_lot",
 "pbjson",
 "pbjson-types",
 "prost 0.12.6",
 "prost-types 0.12.6",
 "serde",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "livekit-runtime"
version = "0.4.0"
source = "git+https://github.com/zed-industries/livekit-rust-sdks?rev=80bb8f4c9112789f7c24cc98d8423010977806a6#80bb8f4c9112789f7c24cc98d8423010977806a6"
dependencies = [
 "tokio",
 "tokio-stream",
]

[[package]]
name = "livekit_api"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "jsonwebtoken",
 "log",
 "prost 0.9.0",
 "prost-build 0.9.0",
 "prost-types 0.9.0",
 "reqwest 0.12.15 (git+https://github.com/zed-industries/reqwest.git?rev=951c770a32f1998d6e999cef3e59e0013e6c4415)",
 "serde",
 "workspace-hack",
]

[[package]]
name = "livekit_client"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "collections",
 "core-foundation 0.10.0",
 "core-video",
 "coreaudio-rs 0.12.1",
 "cpal",
 "futures 0.3.31",
 "gpui",
 "gpui_tokio",
 "http_client_tls",
 "image",
 "libwebrtc",
 "livekit",
 "livekit_api",
 "log",
 "nanoid",
 "objc",
 "parking_lot",
 "postage",
 "scap",
 "serde",
 "serde_json",
 "sha2",
 "simplelog",
 "smallvec",
 "tokio-tungstenite 0.26.2",
 "util",
 "workspace-hack",
]

[[package]]
name = "lmdb-master-sys"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "864808e0b19fb6dd3b70ba94ee671b82fce17554cf80aeb0a155c65bb08027df"
dependencies = [
 "cc",
 "doxygen-rs",
 "libc",
]

[[package]]
name = "lmstudio"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "workspace-hack",
]

[[package]]
name = "lock_api"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07af8b9cdd281b7915f413fa73f29ebd5d55d0d3f0155584dade1ff18cea1b17"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13dc2df351e3202783a1fe0d44375f7295ffb4049267b0f3018346dc122a1d94"
dependencies = [
 "serde",
 "value-bag",
]

[[package]]
name = "logos"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab6f536c1af4c7cc81edf73da1f8029896e7e1e16a219ef09b184e76a296f3db"
dependencies = [
 "logos-derive",
]

[[package]]
name = "logos-codegen"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "189bbfd0b61330abea797e5e9276408f2edbe4f822d7ad08685d67419aafb34e"
dependencies = [
 "beef",
 "fnv",
 "lazy_static",
 "proc-macro2",
 "quote",
 "regex-syntax 0.8.5",
 "rustc_version",
 "syn 2.0.101",
]

[[package]]
name = "logos-derive"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebfe8e1a19049ddbfccbd14ac834b215e11b85b90bab0c2dba7c7b92fb5d5cba"
dependencies = [
 "logos-codegen",
]

[[package]]
name = "loom"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "419e0dc8046cb947daa77eb95ae174acfbddb7673b4151f56d1eed8e93fbfaca"
dependencies = [
 "cfg-if",
 "generator",
 "scoped-tls",
 "tracing",
 "tracing-subscriber",
]

[[package]]
name = "loop9"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fae87c125b03c1d2c0150c90365d7d6bcc53fb73a9acaef207d2d065860f062"
dependencies = [
 "imgref",
]

[[package]]
name = "lru"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234cf4f4a04dc1f57e24b96cc0cd600cf2af460d4161ac5ecdd0af8e1f3b2a38"
dependencies = [
 "hashbrown 0.15.3",
]

[[package]]
name = "lsp"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-pipe",
 "collections",
 "ctor",
 "futures 0.3.31",
 "gpui",
 "log",
 "lsp-types",
 "parking_lot",
 "postage",
 "release_channel",
 "schemars",
 "serde",
 "serde_json",
 "smol",
 "util",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "lsp-types"
version = "0.95.1"
source = "git+https://github.com/zed-industries/lsp-types?rev=c9c189f1c5dd53c624a419ce35bc77ad6a908d18#c9c189f1c5dd53c624a419ce35bc77ad6a908d18"
dependencies = [
 "bitflags 1.3.2",
 "serde",
 "serde_json",
 "serde_repr",
 "url",
]

[[package]]
name = "lyon"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e7f9cda98b5430809e63ca5197b06c7d191bf7e26dfc467d5a3f0290e2a74f"
dependencies = [
 "lyon_algorithms",
 "lyon_extra",
 "lyon_tessellation",
]

[[package]]
name = "lyon_algorithms"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f13c9be19d257c7d37e70608ed858e8eab4b2afcea2e3c9a622e892acbf43c08"
dependencies = [
 "lyon_path",
 "num-traits",
]

[[package]]
name = "lyon_extra"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ca94c7bf1e2557c2798989c43416822c12fc5dcc5e17cc3307ef0e71894a955"
dependencies = [
 "lyon_path",
 "thiserror 1.0.69",
]

[[package]]
name = "lyon_geom"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8af69edc087272df438b3ee436c4bb6d7c04aa8af665cfd398feae627dbd8570"
dependencies = [
 "arrayvec",
 "euclid",
 "num-traits",
]

[[package]]
name = "lyon_path"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0047f508cd7a85ad6bad9518f68cce7b1bf6b943fb71f6da0ee3bc1e8cb75f25"
dependencies = [
 "lyon_geom",
 "num-traits",
]

[[package]]
name = "lyon_tessellation"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "579d42360a4b09846eff2feef28f538696c7d6c7439bfa65874ff3cbe0951b2c"
dependencies = [
 "float_next_after",
 "lyon_path",
 "num-traits",
]

[[package]]
name = "mac"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c41e0c4fef86961ac6d6f8a82609f55f31b05e4fce149ac5710e439df7619ba4"

[[package]]
name = "mach2"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b955cdeb2a02b9117f121ce63aa52d08ade45de53e48fe6a38b39c10f6f709"
dependencies = [
 "libc",
]

[[package]]
name = "malloc_buf"
version = "0.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62bb907fe88d54d8d9ce32a3cceab4218ed2f6b7d35617cafe9adf84e43919cb"
dependencies = [
 "libc",
]

[[package]]
name = "maplit"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e2e65a1a2e43cfcb47a895c4c8b10d1f4a61097f9f254f183aee60cad9c651d"

[[package]]
name = "markdown"
version = "0.1.0"
dependencies = [
 "assets",
 "base64 0.22.1",
 "env_logger 0.11.8",
 "futures 0.3.31",
 "gpui",
 "language",
 "languages",
 "linkify",
 "log",
 "node_runtime",
 "pulldown-cmark 0.12.2",
 "settings",
 "sum_tree",
 "theme",
 "ui",
 "util",
 "workspace-hack",
]

[[package]]
name = "markdown_preview"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-recursion 1.1.1",
 "collections",
 "editor",
 "fs",
 "gpui",
 "language",
 "linkify",
 "log",
 "pretty_assertions",
 "pulldown-cmark 0.12.2",
 "settings",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "markup5ever"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16ce3abbeba692c8b8441d036ef91aea6df8da2c6b6e21c7e14d3c18e526be45"
dependencies = [
 "log",
 "phf",
 "phf_codegen",
 "string_cache",
 "string_cache_codegen",
 "tendril",
]

[[package]]
name = "markup5ever"
version = "0.16.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a8096766c229e8c88a3900c9b44b7e06aa7f7343cc229158c3e58ef8f9973a"
dependencies = [
 "log",
 "tendril",
 "web_atoms",
]

[[package]]
name = "markup5ever_rcdom"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edaa21ab3701bfee5099ade5f7e1f84553fd19228cf332f13cd6e964bf59be18"
dependencies = [
 "html5ever 0.27.0",
 "markup5ever 0.12.1",
 "tendril",
 "xml5ever",
]

[[package]]
name = "match_token"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88a9689d8d44bf9964484516275f5cd4c9b59457a6940c1d5d0ecbb94510a36b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "matchers"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8263075bb86c5a1b1427b5ae862e8889656f126e9f77c484496e8b47cf5c5558"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "matchit"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7465ac9959cc2b1404e8e2367b43684a6d13790fe23056cc8c6c5a6b7bcb94"

[[package]]
name = "maybe-async"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cf92c10c7e361d6b99666ec1c6f9805b0bea2c3bd8c78dc6fe98ac5bd78db11"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "maybe-owned"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4facc753ae494aeb6e3c22f839b158aebd4f9270f55cd3c79906c45476c47ab4"

[[package]]
name = "maybe-rayon"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ea1f30cedd69f0a2954655f7188c6a834246d2bcf1e315e2ac40c4b24dc9519"
dependencies = [
 "cfg-if",
 "rayon",
]

[[package]]
name = "md-5"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d89e7ee0cfbedfc4da3340218492196241d89eefb6dab27de5df917a6d2e78cf"
dependencies = [
 "cfg-if",
 "digest",
]

[[package]]
name = "mdbook"
version = "0.4.48"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6fbb4ac2d9fd7aa987c3510309ea3c80004a968d063c42f0d34fea070817c1"
dependencies = [
 "ammonia",
 "anyhow",
 "chrono",
 "clap",
 "clap_complete",
 "elasticlunr-rs",
 "env_logger 0.11.8",
 "futures-util",
 "handlebars 6.3.2",
 "hex",
 "ignore",
 "log",
 "memchr",
 "notify",
 "notify-debouncer-mini",
 "once_cell",
 "opener",
 "pathdiff",
 "pulldown-cmark 0.10.3",
 "regex",
 "serde",
 "serde_json",
 "sha2",
 "shlex",
 "tempfile",
 "tokio",
 "toml 0.5.11",
 "topological-sort",
 "walkdir",
 "warp",
]

[[package]]
name = "media"
version = "0.1.0"
dependencies = [
 "anyhow",
 "bindgen 0.71.1",
 "core-foundation 0.10.0",
 "core-video",
 "ctor",
 "foreign-types 0.5.0",
 "metal",
 "objc",
 "workspace-hack",
]

[[package]]
name = "memchr"
version = "2.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3"

[[package]]
name = "memfd"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2cffa4ad52c6f791f4f8b15f0c05f9824b2ced1160e88cc393d64fff9a8ac64"
dependencies = [
 "rustix 0.38.44",
]

[[package]]
name = "memmap2"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd3f7eed9d3848f8b98834af67102b720745c4ec028fcd0aa0239277e7de374f"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "488016bfae457b036d996092f6cb448677611ce4449e970ceaf42695203f218a"
dependencies = [
 "autocfg",
]

[[package]]
name = "menu"
version = "0.1.0"
dependencies = [
 "gpui",
 "workspace-hack",
]

[[package]]
name = "metal"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ecfd3296f8c56b7c1f6fbac3c71cefa9d78ce009850c45000015f206dc7fa21"
dependencies = [
 "bitflags 2.9.0",
 "block",
 "core-graphics-types 0.1.3",
 "foreign-types 0.5.0",
 "log",
 "objc",
 "paste",
]

[[package]]
name = "migrator"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "convert_case 0.8.0",
 "log",
 "pretty_assertions",
 "streaming-iterator",
 "tree-sitter",
 "tree-sitter-json",
 "workspace-hack",
]

[[package]]
name = "mimalloc"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "995942f432bbb4822a7e9c3faa87a695185b0d09273ba85f097b54f4e458f2af"
dependencies = [
 "libmimalloc-sys",
]

[[package]]
name = "mime"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6877bb514081ee2a7ff5ef9de3281f14a4dd4bceac4c09388074a6b5df8a139a"

[[package]]
name = "mime_guess"
version = "2.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7c44f8e672c00fe5308fa235f821cb4198414e1c77935c1ab6948d3fd78550e"
dependencies = [
 "mime",
 "unicase",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.8.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3be647b768db090acb35d5ec5db2b0e1f1de11133ca123b9eacf5137868f892a"
dependencies = [
 "adler2",
 "simd-adler32",
]

[[package]]
name = "mint"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e53debba6bda7a793e5f99b8dacf19e626084f525f7829104ba9898f367d85ff"

[[package]]
name = "mio"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2886843bf800fba2e3377cff24abf6379b4c4d5c6681eaf9ea5b0d15090450bd"
dependencies = [
 "libc",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.52.0",
]

[[package]]
name = "miow"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "359f76430b20a79f9e20e115b3428614e654f04fab314482fc0fda0ebd3c6044"
dependencies = [
 "windows-sys 0.48.0",
]

[[package]]
name = "mistral"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "strum 0.27.1",
 "workspace-hack",
]

[[package]]
name = "moka"
version = "0.12.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9321642ca94a4282428e6ea4af8cc2ca4eac48ac7a6a4ea8f33f76d0ce70926"
dependencies = [
 "crossbeam-channel",
 "crossbeam-epoch",
 "crossbeam-utils",
 "loom",
 "parking_lot",
 "portable-atomic",
 "rustc_version",
 "smallvec",
 "tagptr",
 "thiserror 1.0.69",
 "uuid",
]

[[package]]
name = "msvc_spectre_libs"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29e871a9861f3664f18b7e04e9301d4edd55090c2dadb4b1c602e26ab32b1f5b"
dependencies = [
 "cc",
]

[[package]]
name = "multi_buffer"
version = "0.1.0"
dependencies = [
 "anyhow",
 "buffer_diff",
 "clock",
 "collections",
 "ctor",
 "gpui",
 "indoc",
 "itertools 0.14.0",
 "language",
 "log",
 "parking_lot",
 "pretty_assertions",
 "project",
 "rand 0.8.5",
 "rope",
 "serde",
 "settings",
 "smallvec",
 "smol",
 "sum_tree",
 "text",
 "theme",
 "tree-sitter",
 "util",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "multimap"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5ce46fe64a9d73be07dcbe690a38ce1b293be448fd8ce1e6c1b8062c9f72c6a"

[[package]]
name = "multimap"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "defc4c55412d89136f966bbb339008b474350e5e6e78d2714439c386b3137a03"

[[package]]
name = "naga"
version = "25.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b977c445f26e49757f9aca3631c3b8b836942cb278d69a92e7b80d3b24da632"
dependencies = [
 "arrayvec",
 "bit-set 0.8.0",
 "bitflags 2.9.0",
 "cfg_aliases 0.2.1",
 "codespan-reporting 0.12.0",
 "half",
 "hashbrown 0.15.3",
 "hexf-parse",
 "indexmap",
 "log",
 "num-traits",
 "once_cell",
 "rustc-hash 1.1.0",
 "spirv",
 "strum 0.26.3",
 "thiserror 2.0.12",
 "unicode-ident",
]

[[package]]
name = "nanoid"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ffa00dec017b5b1a8b7cf5e2c008bfda1aa7e0697ac1508b491fdf2622fb4d8"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "nanorand"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a51313c5820b0b02bd422f4b44776fbf47961755c74ce64afc73bfad10226c3"
dependencies = [
 "getrandom 0.2.15",
]

[[package]]
name = "native-tls"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87de3442987e9dbec73158d5c715e7ad9072fda936bb03d19d7fa10e00520f0e"
dependencies = [
 "libc",
 "log",
 "openssl",
 "openssl-probe",
 "openssl-sys",
 "schannel",
 "security-framework 2.11.1",
 "security-framework-sys",
 "tempfile",
]

[[package]]
name = "nbformat"
version = "0.10.0"
source = "git+https://github.com/ConradIrwin/runtimed?rev=7130c804216b6914355d15d0b91ea91f6babd734#7130c804216b6914355d15d0b91ea91f6babd734"
dependencies = [
 "anyhow",
 "chrono",
 "jupyter-protocol",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "uuid",
]

[[package]]
name = "ndk"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2076a31b7010b17a38c01907c45b945e8f11495ee4dd588309718901b1f7a5b7"
dependencies = [
 "bitflags 2.9.0",
 "jni-sys",
 "log",
 "ndk-sys",
 "num_enum",
 "thiserror 1.0.69",
]

[[package]]
name = "ndk-context"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "27b02d87554356db9e9a873add8782d4ea6e3e58ea071a9adb9a2e8ddb884a8b"

[[package]]
name = "ndk-sys"
version = "0.5.0+25.2.9519653"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c196769dd60fd4f363e11d948139556a344e79d451aeb2fa2fd040738ef7691"
dependencies = [
 "jni-sys",
]

[[package]]
name = "new_debug_unreachable"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "650eef8c711430f1a879fdd01d4745a7deea475becfb90269c06775983bbf086"

[[package]]
name = "nix"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab2156c4fce2f8df6c499cc1c763e4394b7482525bf2a9701c9d79d215f519e4"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "cfg_aliases 0.1.1",
 "libc",
]

[[package]]
name = "nix"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71e2746dc3a24dd78b3cfcb7be93368c6de9963d30f43a6a73998a9cf4b17b46"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "cfg_aliases 0.2.1",
 "libc",
 "memoffset",
]

[[package]]
name = "nix"
version = "0.30.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74523f3a35e05aba87a1d978330aef40f67b0304ac79c1c00b294c9830543db6"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "cfg_aliases 0.2.1",
 "libc",
]

[[package]]
name = "node_runtime"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-compression",
 "async-std",
 "async-tar",
 "async-trait",
 "futures 0.3.31",
 "http_client",
 "log",
 "paths",
 "semver",
 "serde",
 "serde_json",
 "smol",
 "util",
 "watch",
 "which 6.0.3",
 "workspace-hack",
]

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "noop_proc_macro"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0676bb32a98c1a483ce53e500a81ad9c3d5b3f7c920c28c24e9cb0980d0b5bc8"

[[package]]
name = "normpath"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8911957c4b1549ac0dc74e30db9c8b0e66ddcd6d7acc33098f4c63a64a6d7ed"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "notifications"
version = "0.1.0"
dependencies = [
 "anyhow",
 "channel",
 "client",
 "collections",
 "component",
 "db",
 "gpui",
 "rpc",
 "settings",
 "sum_tree",
 "time",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "notify"
version = "8.0.0"
source = "git+https://github.com/zed-industries/notify.git?rev=bbb9ea5ae52b253e095737847e367c30653a2e96#bbb9ea5ae52b253e095737847e367c30653a2e96"
dependencies = [
 "bitflags 2.9.0",
 "filetime",
 "fsevent-sys 4.1.0",
 "inotify",
 "kqueue",
 "libc",
 "log",
 "mio",
 "notify-types",
 "walkdir",
 "windows-sys 0.59.0",
]

[[package]]
name = "notify-debouncer-mini"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a689eb4262184d9a1727f9087cd03883ea716682ab03ed24efec57d7716dccb8"
dependencies = [
 "log",
 "notify",
 "notify-types",
 "tempfile",
]

[[package]]
name = "notify-types"
version = "2.0.0"
source = "git+https://github.com/zed-industries/notify.git?rev=bbb9ea5ae52b253e095737847e367c30653a2e96#bbb9ea5ae52b253e095737847e367c30653a2e96"

[[package]]
name = "ntapi"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a3895c6391c39d7fe7ebc444a87eb2991b2a0bc718fdabd071eec617fc68e4"
dependencies = [
 "winapi",
]

[[package]]
name = "nu-ansi-term"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a8165726e8236064dbb45459242600304b42a5ea24ee2948e18e023bf7ba84"
dependencies = [
 "overload",
 "winapi",
]

[[package]]
name = "nu-ansi-term"
version = "0.50.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4a28e057d01f97e61255210fcff094d74ed0466038633e95017f5beb68e4399"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "num"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35bd024e8b2ff75562e5f34e7f4905839deb4b22955ef5e73d2fea1b9813cb23"
dependencies = [
 "num-bigint",
 "num-complex",
 "num-integer",
 "num-iter",
 "num-rational",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5e44f723f1133c9deac646763579fdb3ac745e418f2a7af9cd0c431da1f20b9"
dependencies = [
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-bigint-dig"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc84195820f291c7697304f3cbdadd1cb7199c0efc917ff5eafd71225c136151"
dependencies = [
 "byteorder",
 "lazy_static",
 "libm",
 "num-integer",
 "num-iter",
 "num-traits",
 "rand 0.8.5",
 "serde",
 "smallvec",
 "zeroize",
]

[[package]]
name = "num-cmp"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63335b2e2c34fae2fb0aa2cecfd9f0832a1e24b3b32ecec612c3426d46dc8aaa"

[[package]]
name = "num-complex"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73f88a1307638156682bada9d7604135552957b7818057dcef22705b4d509495"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3955f1a9c7c0c15e092f9c887db08b1fc683305fdf6eb6684f22555355e202"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "num-format"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652d9771a63711fd3c3deb670acfbe5c30a4072e664d7a3bf5a9e1056ac72c3"
dependencies = [
 "arrayvec",
 "itoa",
]

[[package]]
name = "num-integer"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7969661fd2958a5cb096e56c8e1ad0444ac2bbcd0061bd28660485a44879858f"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1429034a0490724d0075ebb2bc9e875d6503c3cf69e235a8941aa757d83ef5bf"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-modular"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17bb261bf36fa7d83f4c294f834e91256769097b3cb505d44831e0a179ac647f"

[[package]]
name = "num-order"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "537b596b97c40fcf8056d153049eb22f481c17ebce72a513ec9286e4986d1bb6"
dependencies = [
 "num-modular",
]

[[package]]
name = "num-rational"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f83d14da390562dca69fc84082e73e548e1ad308d24accdedd2720017cb37824"
dependencies = [
 "num-bigint",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
]

[[package]]
name = "num_enum"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e613fc340b2220f734a8595782c551f1250e969d87d3be1ae0579e8d4065179"
dependencies = [
 "num_enum_derive",
]

[[package]]
name = "num_enum_derive"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af1844ef2428cc3e1cb900be36181049ef3d3193c63e43026cfe202983b27a56"
dependencies = [
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "num_threads"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c7398b9c8b70908f6371f47ed36737907c87c52af34c268fed0bf0ceb92ead9"
dependencies = [
 "libc",
]

[[package]]
name = "nvim-rs"
version = "0.9.2"
source = "git+https://github.com/KillTheMule/nvim-rs?rev=764dd270c642f77f10f3e19d05cc178a6cbe69f3#764dd270c642f77f10f3e19d05cc178a6cbe69f3"
dependencies = [
 "async-trait",
 "futures 0.3.31",
 "log",
 "rmp",
 "rmpv",
 "tokio",
 "tokio-util",
]

[[package]]
name = "objc"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "915b1b472bc21c53464d6c8461c9d3af805ba1ef837e1cac254428f4a77177b1"
dependencies = [
 "malloc_buf",
 "objc_exception",
]

[[package]]
name = "objc-foundation"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1add1b659e36c9607c7aab864a76c7a4c2760cd0cd2e120f3fb8b952c7e22bf9"
dependencies = [
 "block",
 "objc",
 "objc_id",
]

[[package]]
name = "objc2"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88c6597e14493ab2e44ce58f2fdecf095a51f12ca57bec060a11c57332520551"
dependencies = [
 "objc2-encode",
]

[[package]]
name = "objc2-app-kit"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6f29f568bec459b0ddff777cec4fe3fd8666d82d5a40ebd0ff7e66134f89bcc"
dependencies = [
 "bitflags 2.9.0",
 "objc2",
 "objc2-core-foundation",
 "objc2-foundation",
 "objc2-quartz-core",
]

[[package]]
name = "objc2-core-foundation"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c10c2894a6fed806ade6027bcd50662746363a9589d3ec9d9bef30a4e4bc166"
dependencies = [
 "bitflags 2.9.0",
 "dispatch2",
 "objc2",
]

[[package]]
name = "objc2-encode"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef25abbcd74fb2609453eb695bd2f860d389e457f67dc17cafc8b8cbc89d0c33"

[[package]]
name = "objc2-foundation"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "900831247d2fe1a09a683278e5384cfb8c80c79fe6b166f9d14bfdde0ea1b03c"
dependencies = [
 "bitflags 2.9.0",
 "objc2",
 "objc2-core-foundation",
]

[[package]]
name = "objc2-metal"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f246c183239540aab1782457b35ab2040d4259175bd1d0c58e46ada7b47a874"
dependencies = [
 "bitflags 2.9.0",
 "block2",
 "dispatch2",
 "objc2",
 "objc2-core-foundation",
 "objc2-foundation",
]

[[package]]
name = "objc2-quartz-core"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90ffb6a0cd5f182dc964334388560b12a57f7b74b3e2dec5e2722aa2dfb2ccd5"
dependencies = [
 "bitflags 2.9.0",
 "objc2",
 "objc2-core-foundation",
 "objc2-foundation",
 "objc2-metal",
]

[[package]]
name = "objc2-ui-kit"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25b1312ad7bc8a0e92adae17aa10f90aae1fb618832f9b993b022b591027daed"
dependencies = [
 "bitflags 2.9.0",
 "objc2",
 "objc2-core-foundation",
 "objc2-foundation",
 "objc2-quartz-core",
]

[[package]]
name = "objc_exception"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad970fb455818ad6cba4c122ad012fae53ae8b4795f86378bce65e4f6bab2ca4"
dependencies = [
 "cc",
]

[[package]]
name = "objc_id"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c92d4ddb4bd7b50d730c215ff871754d0da6b2178849f8a2a2ab69712d0c073b"
dependencies = [
 "objc",
]

[[package]]
name = "object"
version = "0.36.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87"
dependencies = [
 "crc32fast",
 "hashbrown 0.15.3",
 "indexmap",
 "memchr",
]

[[package]]
name = "oboe"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8b61bebd49e5d43f5f8cc7ee2891c16e0f41ec7954d36bcb6c14c5e0de867fb"
dependencies = [
 "jni",
 "ndk",
 "ndk-context",
 "num-derive",
 "num-traits",
 "oboe-sys",
]

[[package]]
name = "oboe-sys"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c8bb09a4a2b1d668170cfe0a7d5bc103f8999fb316c98099b6a9939c9f2e79d"
dependencies = [
 "cc",
]

[[package]]
name = "ollama"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "workspace-hack",
]

[[package]]
name = "once_cell"
version = "1.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42f5e15c9953c5e4ccceeb2e7382a716482c34515315f7b03532b8b4e8393d2d"

[[package]]
name = "oo7"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6cb23d3ec3527d65a83be1c1795cb883c52cfa57147d42acc797127df56fc489"
dependencies = [
 "aes",
 "ashpd",
 "async-fs",
 "async-io",
 "async-lock",
 "blocking",
 "cbc",
 "cipher",
 "digest",
 "endi",
 "futures-lite 2.6.0",
 "futures-util",
 "getrandom 0.3.2",
 "hkdf",
 "hmac",
 "md-5",
 "num",
 "num-bigint-dig",
 "pbkdf2 0.12.2",
 "rand 0.9.1",
 "serde",
 "sha2",
 "subtle",
 "zbus",
 "zbus_macros",
 "zeroize",
 "zvariant",
]

[[package]]
name = "oorandom"
version = "11.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6790f58c7ff633d8771f42965289203411a5e5c68388703c06e14f24770b41e"

[[package]]
name = "open"
version = "5.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2483562e62ea94312f3576a7aca397306df7990b8d89033e18766744377ef95"
dependencies = [
 "is-wsl",
 "libc",
 "pathdiff",
]

[[package]]
name = "open_ai"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "strum 0.27.1",
 "workspace-hack",
]

[[package]]
name = "open_router"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "schemars",
 "serde",
 "serde_json",
 "workspace-hack",
]

[[package]]
name = "opener"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0812e5e4df08da354c851a3376fead46db31c2214f849d3de356d774d057681"
dependencies = [
 "bstr",
 "dbus",
 "normpath",
 "windows-sys 0.59.0",
]

[[package]]
name = "openssl"
version = "0.10.72"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fedfea7d58a1f73118430a55da6a286e7b044961736ce96a16a17068ea25e5da"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "foreign-types 0.3.2",
 "libc",
 "once_cell",
 "openssl-macros",
 "openssl-sys",
]

[[package]]
name = "openssl-macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a948666b637a0f465e8564c73e89d4dde00d72d4d473cc972f390fc3dcee7d9c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "openssl-probe"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d05e27ee213611ffe7d6348b942e8f942b37114c00cc03cec254295a4a17852e"

[[package]]
name = "openssl-sys"
version = "0.9.107"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8288979acd84749c744a9014b4382d42b8f7b2592847b5afb2ed29e5d16ede07"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "optfield"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa59f025cde9c698fcb4fcb3533db4621795374065bee908215263488f2d2a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "option-ext"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04744f49eae99ab78e0d5c0b603ab218f515ea8cfe5a456d7629ad883a3b6e7d"

[[package]]
name = "ordered-float"
version = "2.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68f19d67e5a2795c94e73e0bb1cc1a7edeb2e28efd39e2e1c9b7a40c1108b11c"
dependencies = [
 "num-traits",
]

[[package]]
name = "ordered-float"
version = "4.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bb71e1b3fa6ca1c61f383464aaf2bb0e2f8e772a1f01d486832464de363b951"
dependencies = [
 "num-traits",
]

[[package]]
name = "ordered-stream"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aa2b01e1d916879f73a53d01d1d6cee68adbb31d6d9177a8cfce093cced1d50"
dependencies = [
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "ouroboros"
version = "0.18.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e0f050db9c44b97a94723127e6be766ac5c340c48f2c4bb3ffa11713744be59"
dependencies = [
 "aliasable",
 "ouroboros_macro",
 "static_assertions",
]

[[package]]
name = "ouroboros_macro"
version = "0.18.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c7028bdd3d43083f6d8d4d5187680d0d3560d54df4cc9d752005268b41e64d0"
dependencies = [
 "heck 0.4.1",
 "proc-macro2",
 "proc-macro2-diagnostics",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "outline"
version = "0.1.0"
dependencies = [
 "editor",
 "fuzzy",
 "gpui",
 "indoc",
 "language",
 "menu",
 "ordered-float 2.10.1",
 "picker",
 "project",
 "rope",
 "serde_json",
 "settings",
 "smol",
 "theme",
 "tree-sitter-rust",
 "tree-sitter-typescript",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "outline_panel"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "db",
 "editor",
 "file_icons",
 "fuzzy",
 "gpui",
 "itertools 0.14.0",
 "language",
 "log",
 "menu",
 "outline",
 "pretty_assertions",
 "project",
 "schemars",
 "search",
 "serde",
 "serde_json",
 "settings",
 "smallvec",
 "smol",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "worktree",
 "zed_actions",
]

[[package]]
name = "outref"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a80800c0488c3a21695ea981a54918fbb37abf04f4d0720c453632255e2ff0e"

[[package]]
name = "overload"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b15813163c1d831bf4a13c3610c05c0d03b39feb07f7e09fa234dac9b15aaf39"

[[package]]
name = "p256"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51f44edd08f51e2ade572f141051021c5af22677e42b7dd28a88155151c33594"
dependencies = [
 "ecdsa",
 "elliptic-curve",
 "sha2",
]

[[package]]
name = "page_size"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30d5b2194ed13191c1999ae0704b7839fb18384fa22e49b57eeaa97d79ce40da"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "palette"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cbf71184cc5ecc2e4e1baccdb21026c20e5fc3dcf63028a086131b3ab00b6e6"
dependencies = [
 "approx",
 "fast-srgb8",
 "palette_derive",
]

[[package]]
name = "palette_derive"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5030daf005bface118c096f510ffb781fc28f9ab6a32ab224d8631be6851d30"
dependencies = [
 "by_address",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "panel"
version = "0.1.0"
dependencies = [
 "editor",
 "gpui",
 "settings",
 "theme",
 "ui",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "parking"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f38d5652c16fde515bb1ecef450ab0f6a219d619a7274976324d5e377f7dceba"

[[package]]
name = "parking_lot"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1bf18183cf54e8d6059647fc3063646a1801cf30896933ec2311622cc4b9a27"
dependencies = [
 "lock_api",
 "parking_lot_core",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall 0.5.11",
 "smallvec",
 "windows-targets 0.52.6",
]

[[package]]
name = "partial-json-fixer"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35ffd90b3f3b6477db7478016b9efb1b7e9d38eafd095f0542fe0ec2ea884a13"

[[package]]
name = "password-hash"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7676374caaee8a325c9e7a2ae557f216c5563a171d6997b0ef8a65af35147700"
dependencies = [
 "base64ct",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "password-hash"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "346f04948ba92c43e8469c1ee6736c7563d71012b17d40745260fe106aac2166"
dependencies = [
 "base64ct",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pathdiff"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df94ce210e5bc13cb6651479fa48d14f601d9858cfe0467f43ae157023b938d3"

[[package]]
name = "pathfinder_geometry"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b7e7b4ea703700ce73ebf128e1450eb69c3a8329199ffbfb9b2a0418e5ad3"
dependencies = [
 "log",
 "pathfinder_simd",
]

[[package]]
name = "pathfinder_simd"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cf07ef4804cfa9aea3b04a7bbdd5a40031dbb6b4f2cbaf2b011666c80c5b4f2"
dependencies = [
 "rustc_version",
]

[[package]]
name = "paths"
version = "0.1.0"
dependencies = [
 "dirs 4.0.0",
 "util",
 "workspace-hack",
]

[[package]]
name = "pbjson"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1030c719b0ec2a2d25a5df729d6cff1acf3cc230bf766f4f97833591f7577b90"
dependencies = [
 "base64 0.21.7",
 "serde",
]

[[package]]
name = "pbjson-build"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2580e33f2292d34be285c5bc3dba5259542b083cfad6037b6d70345f24dcb735"
dependencies = [
 "heck 0.4.1",
 "itertools 0.11.0",
 "prost 0.12.6",
 "prost-types 0.12.6",
]

[[package]]
name = "pbjson-types"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18f596653ba4ac51bdecbb4ef6773bc7f56042dc13927910de1684ad3d32aa12"
dependencies = [
 "bytes 1.10.1",
 "chrono",
 "pbjson",
 "pbjson-build",
 "prost 0.12.6",
 "prost-build 0.12.6",
 "serde",
]

[[package]]
name = "pbkdf2"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83a0692ec44e4cf1ef28ca317f14f8f07da2d95ec3fa01f86e4467b725e60917"
dependencies = [
 "digest",
 "hmac",
 "password-hash 0.4.2",
 "sha2",
]

[[package]]
name = "pbkdf2"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8ed6a7761f76e3b9f92dfb0a60a6a6477c61024b775147ff0973a02653abaf2"
dependencies = [
 "digest",
 "hmac",
]

[[package]]
name = "pem"
version = "3.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38af38e8470ac9dee3ce1bae1af9c1671fffc44ddfd8bd1d0a3445bf349a8ef3"
dependencies = [
 "base64 0.22.1",
 "serde",
]

[[package]]
name = "pem-rfc7468"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88b39c9bfcfc231068454382784bb460aae594343fb030d46e9f50a645418412"
dependencies = [
 "base64ct",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "perplexity"
version = "0.1.0"
dependencies = [
 "serde",
 "zed_extension_api 0.6.0",
]

[[package]]
name = "pest"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "198db74531d58c70a361c42201efde7e2591e976d518caf7662a47dc5720e7b6"
dependencies = [
 "memchr",
 "thiserror 2.0.12",
 "ucd-trie",
]

[[package]]
name = "pest_derive"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d725d9cfd79e87dccc9341a2ef39d1b6f6353d68c4b33c177febbe1a402c97c5"
dependencies = [
 "pest",
 "pest_generator",
]

[[package]]
name = "pest_generator"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db7d01726be8ab66ab32f9df467ae8b1148906685bbe75c82d1e65d7f5b3f841"
dependencies = [
 "pest",
 "pest_meta",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "pest_meta"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f9f832470494906d1fca5329f8ab5791cc60beb230c74815dff541cbd2b5ca0"
dependencies = [
 "once_cell",
 "pest",
 "sha2",
]

[[package]]
name = "pet"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "clap",
 "env_logger 0.10.2",
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-conda",
 "pet-core",
 "pet-env-var-path",
 "pet-fs",
 "pet-global-virtualenvs",
 "pet-homebrew",
 "pet-jsonrpc",
 "pet-linux-global-python",
 "pet-mac-commandlinetools",
 "pet-mac-python-org",
 "pet-mac-xcode",
 "pet-pipenv",
 "pet-pixi",
 "pet-poetry",
 "pet-pyenv",
 "pet-python-utils",
 "pet-reporter",
 "pet-telemetry",
 "pet-venv",
 "pet-virtualenv",
 "pet-virtualenvwrapper",
 "pet-windows-registry",
 "pet-windows-store",
 "serde",
 "serde_json",
]

[[package]]
name = "pet-conda"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "env_logger 0.10.2",
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-reporter",
 "regex",
 "serde",
 "serde_json",
 "yaml-rust2",
]

[[package]]
name = "pet-core"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "clap",
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-fs",
 "regex",
 "serde",
 "serde_json",
]

[[package]]
name = "pet-env-var-path"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-conda",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
 "regex",
]

[[package]]
name = "pet-fs"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
]

[[package]]
name = "pet-global-virtualenvs"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-conda",
 "pet-core",
 "pet-fs",
 "pet-virtualenv",
]

[[package]]
name = "pet-homebrew"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-conda",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
 "regex",
 "serde",
 "serde_json",
]

[[package]]
name = "pet-jsonrpc"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "env_logger 0.10.2",
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "serde",
 "serde_json",
]

[[package]]
name = "pet-linux-global-python"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
]

[[package]]
name = "pet-mac-commandlinetools"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
]

[[package]]
name = "pet-mac-python-org"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
]

[[package]]
name = "pet-mac-xcode"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
]

[[package]]
name = "pet-pipenv"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
]

[[package]]
name = "pet-pixi"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-conda",
 "pet-core",
 "pet-python-utils",
]

[[package]]
name = "pet-poetry"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "base64 0.22.1",
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-reporter",
 "pet-virtualenv",
 "regex",
 "serde",
 "serde_json",
 "sha2",
 "toml 0.8.20",
]

[[package]]
name = "pet-pyenv"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-conda",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-reporter",
 "regex",
 "serde",
 "serde_json",
]

[[package]]
name = "pet-python-utils"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "env_logger 0.10.2",
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "regex",
 "serde",
 "serde_json",
 "sha2",
]

[[package]]
name = "pet-reporter"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "env_logger 0.10.2",
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-jsonrpc",
 "serde",
 "serde_json",
]

[[package]]
name = "pet-telemetry"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "env_logger 0.10.2",
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "regex",
]

[[package]]
name = "pet-venv"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-python-utils",
 "pet-virtualenv",
]

[[package]]
name = "pet-virtualenv"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
]

[[package]]
name = "pet-virtualenvwrapper"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
]

[[package]]
name = "pet-windows-registry"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-conda",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
 "pet-windows-store",
 "regex",
 "winreg 0.55.0",
]

[[package]]
name = "pet-windows-store"
version = "0.1.0"
source = "git+https://github.com/microsoft/python-environment-tools.git?rev=845945b830297a50de0e24020b980a65e4820559#845945b830297a50de0e24020b980a65e4820559"
dependencies = [
 "lazy_static",
 "log",
 "msvc_spectre_libs",
 "pet-core",
 "pet-fs",
 "pet-python-utils",
 "pet-virtualenv",
 "regex",
 "winreg 0.55.0",
]

[[package]]
name = "petgraph"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4c5cc86750666a3ed20bdaf5ca2a0344f9c67674cae0515bec2da16fbaa47db"
dependencies = [
 "fixedbitset",
 "indexmap",
]

[[package]]
name = "pgvector"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0e8871b6d7ca78348c6cd29b911b94851f3429f0cd403130ca17f26c1fb91a6"
dependencies = [
 "serde",
]

[[package]]
name = "phf"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd6780a80ae0c52cc120a26a1a42c1ae51b247a253e4e06113d23d2c2edd078"
dependencies = [
 "phf_macros",
 "phf_shared",
]

[[package]]
name = "phf_codegen"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aef8048c789fa5e851558d709946d6d79a8ff88c0440c587967f8e94bfb1216a"
dependencies = [
 "phf_generator",
 "phf_shared",
]

[[package]]
name = "phf_generator"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c80231409c20246a13fddb31776fb942c38553c51e871f8cbd687a4cfb5843d"
dependencies = [
 "phf_shared",
 "rand 0.8.5",
]

[[package]]
name = "phf_macros"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f84ac04429c13a7ff43785d75ad27569f2951ce0ffd30a3321230db2fc727216"
dependencies = [
 "phf_generator",
 "phf_shared",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "phf_shared"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67eabc2ef2a60eb7faa00097bd1ffdb5bd28e62bf39990626a582201b7a754e5"
dependencies = [
 "siphasher",
]

[[package]]
name = "picker"
version = "0.1.0"
dependencies = [
 "anyhow",
 "ctor",
 "editor",
 "env_logger 0.11.8",
 "gpui",
 "menu",
 "schemars",
 "serde",
 "serde_json",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "pico-args"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5be167a7af36ee22fe3115051bc51f6e6c7054c9348e28deb4f49bd6f705a315"

[[package]]
name = "pin-project"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677f1add503faace112b9f1373e43e9e054bfdd22ff1a63c1bc485eaec6a6a8a"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e918e4ff8c4549eb882f14b3a4bc8c8bc93de829416eacf579f1207a8fbf861"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "pin-project-lite"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b3cff922bd51709b605d9ead9aa71031d81447142d828eb4a6eba76fe619f9b"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "piper"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96c8c490f422ef9a4efd2cb5b42b76c8613d7e7dfc1caf667b8a3350a5acc066"
dependencies = [
 "atomic-waker",
 "fastrand 2.3.0",
 "futures-io",
]

[[package]]
name = "pkcs1"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8ffb9f10fa047879315e6625af03c164b16962a5368d724ed16323b68ace47f"
dependencies = [
 "der 0.7.10",
 "pkcs8 0.10.2",
 "spki 0.7.3",
]

[[package]]
name = "pkcs8"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9eca2c590a5f85da82668fa685c09ce2888b9430e83299debf1f34b65fd4a4ba"
dependencies = [
 "der 0.6.1",
 "spki 0.6.0",
]

[[package]]
name = "pkcs8"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f950b2377845cebe5cf8b5165cb3cc1a5e0fa5cfa3e1f7f55707d8fd82e0a7b7"
dependencies = [
 "der 0.7.10",
 "spki 0.7.3",
]

[[package]]
name = "pkg-config"
version = "0.3.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7edddbd0b52d732b21ad9a5fab5c704c14cd949e5e9a1ec5929a24fded1b904c"

[[package]]
name = "plist"
version = "1.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eac26e981c03a6e53e0aee43c113e3202f5581d5360dae7bd2c70e800dd0451d"
dependencies = [
 "base64 0.22.1",
 "indexmap",
 "quick-xml 0.32.0",
 "serde",
 "time",
]

[[package]]
name = "plotters"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5aeb6f403d7a4911efb1e33402027fc44f29b5bf6def3effcc22d7bb75f2b747"
dependencies = [
 "num-traits",
 "plotters-backend",
 "plotters-svg",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "plotters-backend"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df42e13c12958a16b3f7f4386b9ab1f3e7933914ecea48da7139435263a4172a"

[[package]]
name = "plotters-svg"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51bae2ac328883f7acdfea3d66a7c35751187f870bc81f94563733a154d7a670"
dependencies = [
 "plotters-backend",
]

[[package]]
name = "png"
version = "0.17.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82151a2fc869e011c153adc57cf2789ccb8d9906ce52c0b39a6b5697749d7526"
dependencies = [
 "bitflags 1.3.2",
 "crc32fast",
 "fdeflate",
 "flate2",
 "miniz_oxide",
]

[[package]]
name = "polling"
version = "3.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a604568c3202727d1507653cb121dbd627a58684eb09a820fd746bee38b4442f"
dependencies = [
 "cfg-if",
 "concurrent-queue",
 "hermit-abi 0.4.0",
 "pin-project-lite",
 "rustix 0.38.44",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "pollster"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5da3b0203fd7ee5720aa0b5e790b591aa5d3f41c3ed2c34a3a393382198af2f7"

[[package]]
name = "pollster"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f3a9f18d041e6d0e102a0a46750538147e5e8992d3b4873aaafee2520b00ce3"

[[package]]
name = "portable-atomic"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "350e9b48cbc6b0e028b0473b114454c6316e57336ee184ceab6e53f72c178b3e"

[[package]]
name = "portable-atomic-util"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8a2f0d8d040d7848a709caf78912debcc3f33ee4b3cac47d73d1e1069e83507"
dependencies = [
 "portable-atomic",
]

[[package]]
name = "portable-pty"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4a596a2b3d2752d94f51fac2d4a96737b8705dddd311a32b9af47211f08671e"
dependencies = [
 "anyhow",
 "bitflags 1.3.2",
 "downcast-rs",
 "filedescriptor",
 "lazy_static",
 "libc",
 "log",
 "nix 0.28.0",
 "serial2",
 "shared_library",
 "shell-words",
 "winapi",
 "winreg 0.10.1",
]

[[package]]
name = "postage"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af3fb618632874fb76937c2361a7f22afd393c982a2165595407edc75b06d3c1"
dependencies = [
 "atomic",
 "crossbeam-queue",
 "futures 0.3.31",
 "log",
 "parking_lot",
 "pin-project",
 "pollster 0.2.5",
 "static_assertions",
 "thiserror 1.0.69",
]

[[package]]
name = "postcard"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "170a2601f67cc9dba8edd8c4870b15f71a6a2dc196daec8c83f72b59dff628a8"
dependencies = [
 "cobs",
 "embedded-io 0.4.0",
 "embedded-io 0.6.1",
 "serde",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "ppv-lite86"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85eae3c4ed2f50dcfe72643da4befc30deadb458a9b590d720cde2f2b1e97da9"
dependencies = [
 "zerocopy 0.8.24",
]

[[package]]
name = "precomputed-hash"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "925383efa346730478fb4838dbe9137d2a47675ad789c546d150a6e1dd4ab31c"

[[package]]
name = "prettier"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "fs",
 "gpui",
 "language",
 "log",
 "lsp",
 "node_runtime",
 "parking_lot",
 "paths",
 "serde",
 "serde_json",
 "util",
 "workspace-hack",
]

[[package]]
name = "pretty_assertions"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ae130e2f271fbc2ac3a40fb1d07180839cdbbe443c7a27e1e3c13c5cac0116d"
dependencies = [
 "diff",
 "yansi",
]

[[package]]
name = "prettyplease"
version = "0.2.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "664ec5419c51e34154eec046ebcba56312d5a2fc3b09a06da188e1ad21afadf6"
dependencies = [
 "proc-macro2",
 "syn 2.0.101",
]

[[package]]
name = "proc-macro-crate"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edce586971a4dfaa28950c6f18ed55e0406c1ab88bbce2c6f6293a7aaba73d35"
dependencies = [
 "toml_edit",
]

[[package]]
name = "proc-macro-error-attr2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96de42df36bb9bba5542fe9f1a054b8cc87e172759a1868aa05c1f3acc89dfc5"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "proc-macro-error2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11ec05c52be0a07b08061f7dd003e7d7092e0472bc731b4af7bb1ef876109802"
dependencies = [
 "proc-macro-error-attr2",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "proc-macro2"
version = "1.0.95"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02b3e5e68a3a1a02aad3ec490a98007cbc13c37cbe84a3cd7b8e406d76e7f778"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "proc-macro2-diagnostics"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af066a9c399a26e020ada66a034357a868728e72cd426f3adcd35f80d88d88c8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "version_check",
 "yansi",
]

[[package]]
name = "prodash"
version = "29.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f04bb108f648884c23b98a0e940ebc2c93c0c3b89f04dbaf7eb8256ce617d1bc"
dependencies = [
 "log",
 "parking_lot",
]

[[package]]
name = "profiling"
version = "1.0.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afbdc74edc00b6f6a218ca6a5364d6226a259d4b8ea1af4a0ea063f27e179f4d"
dependencies = [
 "profiling-procmacros",
]

[[package]]
name = "profiling-procmacros"
version = "1.0.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a65f2e60fbf1063868558d69c6beacf412dc755f9fc020f514b7955fc914fe30"
dependencies = [
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "project"
version = "0.1.0"
dependencies = [
 "aho-corasick",
 "anyhow",
 "askpass",
 "async-trait",
 "buffer_diff",
 "circular-buffer",
 "client",
 "clock",
 "collections",
 "context_server",
 "dap",
 "dap_adapters",
 "extension",
 "fancy-regex 0.14.0",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "git",
 "git2",
 "git_hosting_providers",
 "globset",
 "gpui",
 "http_client",
 "image",
 "indexmap",
 "itertools 0.14.0",
 "language",
 "log",
 "lsp",
 "node_runtime",
 "parking_lot",
 "pathdiff",
 "paths",
 "postage",
 "prettier",
 "pretty_assertions",
 "rand 0.8.5",
 "regex",
 "release_channel",
 "remote",
 "rpc",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "sha2",
 "shellexpand 2.1.2",
 "shlex",
 "smol",
 "snippet",
 "snippet_provider",
 "sum_tree",
 "task",
 "tempfile",
 "terminal",
 "text",
 "toml 0.8.20",
 "unindent",
 "url",
 "util",
 "which 6.0.3",
 "workspace-hack",
 "worktree",
 "zlog",
]

[[package]]
name = "project_panel"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "collections",
 "command_palette_hooks",
 "db",
 "editor",
 "file_icons",
 "git",
 "gpui",
 "indexmap",
 "language",
 "menu",
 "pretty_assertions",
 "project",
 "schemars",
 "search",
 "serde",
 "serde_derive",
 "serde_json",
 "settings",
 "smallvec",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "worktree",
 "zed_actions",
]

[[package]]
name = "project_symbols"
version = "0.1.0"
dependencies = [
 "anyhow",
 "editor",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "language",
 "lsp",
 "ordered-float 2.10.1",
 "picker",
 "project",
 "release_channel",
 "serde_json",
 "settings",
 "theme",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "prometheus"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ca5326d8d0b950a9acd87e6a3f94745394f62e4dae1b1ee22b2bc0c394af43a"
dependencies = [
 "cfg-if",
 "fnv",
 "lazy_static",
 "memchr",
 "parking_lot",
 "protobuf",
 "thiserror 2.0.12",
]

[[package]]
name = "prompt_store"
version = "0.1.0"
dependencies = [
 "anyhow",
 "assets",
 "chrono",
 "collections",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "handlebars 4.5.0",
 "heed",
 "language",
 "log",
 "parking_lot",
 "paths",
 "rope",
 "serde",
 "serde_json",
 "text",
 "util",
 "uuid",
 "workspace-hack",
]

[[package]]
name = "prost"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "444879275cb4fd84958b1a1d5420d15e6fcf7c235fe47f053c9c2a80aceb6001"
dependencies = [
 "bytes 1.10.1",
 "prost-derive 0.9.0",
]

[[package]]
name = "prost"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "deb1435c188b76130da55f17a466d252ff7b1418b2ad3e037d127b94e3411f29"
dependencies = [
 "bytes 1.10.1",
 "prost-derive 0.12.6",
]

[[package]]
name = "prost"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2796faa41db3ec313a31f7624d9286acf277b52de526150b7e69f3debf891ee5"
dependencies = [
 "bytes 1.10.1",
 "prost-derive 0.13.5",
]

[[package]]
name = "prost-build"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62941722fb675d463659e49c4f3fe1fe792ff24fe5bbaa9c08cd3b98a1c354f5"
dependencies = [
 "bytes 1.10.1",
 "heck 0.3.3",
 "itertools 0.10.5",
 "lazy_static",
 "log",
 "multimap 0.8.3",
 "petgraph",
 "prost 0.9.0",
 "prost-types 0.9.0",
 "regex",
 "tempfile",
 "which 4.4.2",
]

[[package]]
name = "prost-build"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22505a5c94da8e3b7c2996394d1c933236c4d743e81a410bcca4e6989fc066a4"
dependencies = [
 "bytes 1.10.1",
 "heck 0.5.0",
 "itertools 0.12.1",
 "log",
 "multimap 0.10.0",
 "once_cell",
 "petgraph",
 "prettyplease",
 "prost 0.12.6",
 "prost-types 0.12.6",
 "regex",
 "syn 2.0.101",
 "tempfile",
]

[[package]]
name = "prost-derive"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9cc1a3263e07e0bf68e96268f37665207b49560d98739662cdfaae215c720fe"
dependencies = [
 "anyhow",
 "itertools 0.10.5",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "prost-derive"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81bddcdb20abf9501610992b6759a4c888aef7d1a7247ef75e2404275ac24af1"
dependencies = [
 "anyhow",
 "itertools 0.12.1",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "prost-derive"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a56d757972c98b346a9b766e3f02746cde6dd1cd1d1d563472929fdd74bec4d"
dependencies = [
 "anyhow",
 "itertools 0.14.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "prost-types"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "534b7a0e836e3c482d2693070f982e39e7611da9695d4d1f5a4b186b51faef0a"
dependencies = [
 "bytes 1.10.1",
 "prost 0.9.0",
]

[[package]]
name = "prost-types"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9091c90b0a32608e984ff2fa4091273cbdd755d54935c51d520887f4a1dbd5b0"
dependencies = [
 "prost 0.12.6",
]

[[package]]
name = "proto"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "prost 0.9.0",
 "prost-build 0.9.0",
 "serde",
 "workspace-hack",
]

[[package]]
name = "protobuf"
version = "3.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d65a1d4ddae7d8b5de68153b48f6aa3bba8cb002b243dbdbc55a5afbc98f99f4"
dependencies = [
 "once_cell",
 "protobuf-support",
 "thiserror 1.0.69",
]

[[package]]
name = "protobuf-support"
version = "3.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e36c2f31e0a47f9280fb347ef5e461ffcd2c52dd520d8e216b52f93b0b0d7d6"
dependencies = [
 "thiserror 1.0.69",
]

[[package]]
name = "psm"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f58e5423e24c18cc840e1c98370b3993c6649cd1678b4d24318bcf0a083cbe88"
dependencies = [
 "cc",
]

[[package]]
name = "ptr_meta"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0738ccf7ea06b608c10564b31debd4f5bc5e197fc8bfe088f68ae5ce81e7a4f1"
dependencies = [
 "ptr_meta_derive",
]

[[package]]
name = "ptr_meta_derive"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16b845dbfca988fa33db069c0e230574d15a3088f147a87b64c7589eb662c9ac"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "pulldown-cmark"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76979bea66e7875e7509c4ec5300112b316af87fa7a252ca91c448b32dfe3993"
dependencies = [
 "bitflags 2.9.0",
 "memchr",
 "pulldown-cmark-escape",
 "unicase",
]

[[package]]
name = "pulldown-cmark"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f86ba2052aebccc42cbbb3ed234b8b13ce76f75c3551a303cb2bcffcff12bb14"
dependencies = [
 "bitflags 2.9.0",
 "memchr",
 "unicase",
]

[[package]]
name = "pulldown-cmark-escape"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd348ff538bc9caeda7ee8cad2d1d48236a1f443c1fa3913c6a02fe0043b1dd3"

[[package]]
name = "pulley-interpreter"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62d95f8575df49a2708398182f49a888cf9dc30210fb1fd2df87c889edcee75d"
dependencies = [
 "cranelift-bitset",
 "log",
 "sptr",
 "wasmtime-math",
]

[[package]]
name = "qoi"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f6d64c71eb498fe9eae14ce4ec935c555749aef511cca85b5568910d6e48001"
dependencies = [
 "bytemuck",
]

[[package]]
name = "quick-error"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a993555f31e5a609f617c12db6250dedcac1b0a85076912c436e6fc9b2c8e6a3"

[[package]]
name = "quick-xml"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eff6510e86862b57b210fd8cbe8ed3f0d7d600b9c2863cd4549a2e033c66e956"
dependencies = [
 "memchr",
]

[[package]]
name = "quick-xml"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d3a6e5838b60e0e8fa7a43f22ade549a37d61f8bdbe636d0d7816191de969c2"
dependencies = [
 "memchr",
]

[[package]]
name = "quick-xml"
version = "0.37.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4ce8c88de324ff838700f36fb6ab86c96df0e3c4ab6ef3a9b2044465cce1369"
dependencies = [
 "memchr",
]

[[package]]
name = "quinn"
version = "0.11.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3bd15a6f2967aef83887dcb9fec0014580467e33720d073560cf015a5683012"
dependencies = [
 "bytes 1.10.1",
 "cfg_aliases 0.2.1",
 "pin-project-lite",
 "quinn-proto",
 "quinn-udp",
 "rustc-hash 2.1.1",
 "rustls 0.23.26",
 "socket2",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-proto"
version = "0.11.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b820744eb4dc9b57a3398183639c511b5a26d2ed702cedd3febaa1393caa22cc"
dependencies = [
 "bytes 1.10.1",
 "getrandom 0.3.2",
 "rand 0.9.1",
 "ring",
 "rustc-hash 2.1.1",
 "rustls 0.23.26",
 "rustls-pki-types",
 "slab",
 "thiserror 2.0.12",
 "tinyvec",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-udp"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "541d0f57c6ec747a90738a52741d3221f7960e8ac2f0ff4b1a63680e033b4ab5"
dependencies = [
 "cfg_aliases 0.2.1",
 "libc",
 "once_cell",
 "socket2",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "quote"
version = "1.0.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1885c039570dc00dcb4ff087a89e185fd56bae234ddc7f056a945bf36467248d"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "r-efi"
version = "5.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74765f6d916ee2faa39bc8e68e4f3ed8949b48cccdac59983d287a7cb71ce9c5"

[[package]]
name = "radium"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc33ff2d4973d518d823d61aa239014831e521c75da58e3df4840d3f47749d09"

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.16",
 "libc",
 "rand_chacha 0.2.2",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
]

[[package]]
name = "rand"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fbfd9d094a40bf3ae768db9361049ace4c0e04a4fd6b359518bd7b73a73dd97"
dependencies = [
 "rand_chacha 0.9.0",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4c8ed856279c9737206bf725bf36935d8666ead7aa69b52be55af369d193402"
dependencies = [
 "ppv-lite86",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3022b5f1df60f26e1ffddd6c66e8aa15de382ae63b3a0c1bfc0e4d3e3f325cb"
dependencies = [
 "ppv-lite86",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.16",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.15",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"
dependencies = [
 "getrandom 0.3.2",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rangemap"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f60fcc7d6849342eff22c4350c8b9a989ee8ceabc4b481253e8946b9fe83d684"

[[package]]
name = "rav1e"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd87ce80a7665b1cce111f8a16c1f3929f6547ce91ade6addf4ec86a8dda5ce9"
dependencies = [
 "arbitrary",
 "arg_enum_proc_macro",
 "arrayvec",
 "av1-grain",
 "bitstream-io",
 "built",
 "cfg-if",
 "interpolate_name",
 "itertools 0.12.1",
 "libc",
 "libfuzzer-sys",
 "log",
 "maybe-rayon",
 "new_debug_unreachable",
 "noop_proc_macro",
 "num-derive",
 "num-traits",
 "once_cell",
 "paste",
 "profiling",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "simd_helpers",
 "system-deps",
 "thiserror 1.0.69",
 "v_frame",
 "wasm-bindgen",
]

[[package]]
name = "ravif"
version = "0.11.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6a5f31fcf7500f9401fea858ea4ab5525c99f2322cfcee732c0e6c74208c0c6"
dependencies = [
 "avif-serialize",
 "imgref",
 "loop9",
 "quick-error",
 "rav1e",
 "rayon",
 "rgb",
]

[[package]]
name = "raw-window-handle"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20675572f6f24e9e76ef639bc5552774ed45f1c30e2951e1e99c59888861c539"

[[package]]
name = "raw-window-metal"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76e8caa82e31bb98fee12fa8f051c94a6aa36b07cddb03f0d4fc558988360ff1"
dependencies = [
 "cocoa 0.25.0",
 "core-graphics 0.23.2",
 "objc",
 "raw-window-handle",
]

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "read-fonts"
version = "0.25.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f9e8a4f503e5c8750e4cd3b32a4e090035c46374b305a15c70bad833dca05f"
dependencies = [
 "bytemuck",
 "font-types",
]

[[package]]
name = "recent_projects"
version = "0.1.0"
dependencies = [
 "anyhow",
 "auto_update",
 "dap",
 "editor",
 "extension_host",
 "file_finder",
 "futures 0.3.31",
 "fuzzy",
 "gpui",
 "language",
 "log",
 "markdown",
 "menu",
 "ordered-float 2.10.1",
 "paths",
 "picker",
 "project",
 "release_channel",
 "remote",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "task",
 "telemetry",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "redox_syscall"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2f103c6d277498fbceb16e84d317e2a400f160f46904d5f5410848c829511a3"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "redox_users"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba009ff324d1fc1b900bd1fdb31564febe58a8ccc8a6fdbb93b543d33b13ca43"
dependencies = [
 "getrandom 0.2.15",
 "libredox",
 "thiserror 1.0.69",
]

[[package]]
name = "redox_users"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd6f9d3d47bdd2ad6945c5015a226ec6155d0bcdfd8f7cd29f86b71f8de99d2b"
dependencies = [
 "getrandom 0.2.15",
 "libredox",
 "thiserror 2.0.12",
]

[[package]]
name = "ref-cast"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a0ae411dbe946a674d89546582cea4ba2bb8defac896622d6496f14c23ba5cf"
dependencies = [
 "ref-cast-impl",
]

[[package]]
name = "ref-cast-impl"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1165225c21bff1f3bbce98f5a1f889949bc902d3575308cc7b0de30b4f6d27c7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "referencing"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8eff4fa778b5c2a57e85c5f2fe3a709c52f0e60d23146e2151cbef5893f420e"
dependencies = [
 "ahash 0.8.11",
 "fluent-uri",
 "once_cell",
 "parking_lot",
 "percent-encoding",
 "serde_json",
]

[[package]]
name = "refineable"
version = "0.1.0"
dependencies = [
 "derive_refineable",
 "workspace-hack",
]

[[package]]
name = "regalloc2"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc06e6b318142614e4a48bc725abbf08ff166694835c43c9dae5a9009704639a"
dependencies = [
 "allocator-api2",
 "bumpalo",
 "hashbrown 0.15.3",
 "log",
 "rustc-hash 2.1.1",
 "serde",
 "smallvec",
]

[[package]]
name = "regex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b544ef1b4eac5dc2db33ea63606ae9ffcfac26c1416a2806ae0bf5f56b201191"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-automata"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c230d73fb8d8c1b9c0b3135c5142a8acee3a0558fb8db5cf1cb65f8d7862132"
dependencies = [
 "regex-syntax 0.6.29",
]

[[package]]
name = "regex-automata"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "809e8dc61f6de73b46c85f4c96486310fe304c434cfa43669d7b40f711150908"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-lite"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53a49587ad06b26609c52e423de037e7f57f20d53535d66e08c695f347df952a"

[[package]]
name = "regex-syntax"
version = "0.6.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f162c6dd7b008981e4d40210aca20b4bd0f9b60ca9271061b07f78537722f2e1"

[[package]]
name = "regex-syntax"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b15c43186be67a4fd63bee50d0303afffcef381492ebe2c5d87f324e1b8815c"

[[package]]
name = "release_channel"
version = "0.1.0"
dependencies = [
 "gpui",
 "workspace-hack",
]

[[package]]
name = "remote"
version = "0.1.0"
dependencies = [
 "anyhow",
 "askpass",
 "async-trait",
 "collections",
 "fs",
 "futures 0.3.31",
 "gpui",
 "itertools 0.14.0",
 "log",
 "parking_lot",
 "paths",
 "prost 0.9.0",
 "release_channel",
 "rpc",
 "schemars",
 "serde",
 "serde_json",
 "shlex",
 "smol",
 "tempfile",
 "thiserror 2.0.12",
 "urlencoding",
 "util",
 "workspace-hack",
]

[[package]]
name = "remote_server"
version = "0.1.0"
dependencies = [
 "anyhow",
 "askpass",
 "assistant_tool",
 "assistant_tools",
 "backtrace",
 "cargo_toml",
 "chrono",
 "clap",
 "client",
 "clock",
 "dap",
 "dap_adapters",
 "debug_adapter_extension",
 "env_logger 0.11.8",
 "extension",
 "extension_host",
 "fork",
 "fs",
 "futures 0.3.31",
 "git",
 "git_hosting_providers",
 "gpui",
 "gpui_tokio",
 "http_client",
 "language",
 "language_extension",
 "language_model",
 "languages",
 "libc",
 "log",
 "lsp",
 "node_runtime",
 "paths",
 "project",
 "proto",
 "release_channel",
 "remote",
 "reqwest_client",
 "rpc",
 "rust-embed",
 "serde",
 "serde_json",
 "settings",
 "shellexpand 2.1.2",
 "smol",
 "sysinfo",
 "telemetry_events",
 "toml 0.8.20",
 "unindent",
 "util",
 "watch",
 "worktree",
 "zlog",
]

[[package]]
name = "rend"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71fe3824f5629716b1589be05dacd749f6aa084c87e00e016714a8cdfccc997c"
dependencies = [
 "bytecheck",
]

[[package]]
name = "repl"
version = "0.1.0"
dependencies = [
 "alacritty_terminal",
 "anyhow",
 "async-dispatcher",
 "async-tungstenite",
 "base64 0.22.1",
 "client",
 "collections",
 "command_palette_hooks",
 "editor",
 "env_logger 0.11.8",
 "feature_flags",
 "file_icons",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "image",
 "indoc",
 "jupyter-protocol",
 "jupyter-websocket-client",
 "language",
 "languages",
 "log",
 "markdown_preview",
 "menu",
 "multi_buffer",
 "nbformat",
 "picker",
 "project",
 "runtimelib",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "telemetry",
 "terminal",
 "terminal_view",
 "theme",
 "tree-sitter-md",
 "tree-sitter-python",
 "tree-sitter-typescript",
 "ui",
 "util",
 "uuid",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "reqwest"
version = "0.11.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd67538700a17451e7cba03ac727fb961abb7607553461627b97de0b89cf4a62"
dependencies = [
 "base64 0.21.7",
 "bytes 1.10.1",
 "encoding_rs",
 "futures-core",
 "futures-util",
 "h2 0.3.26",
 "http 0.2.12",
 "http-body 0.4.6",
 "hyper 0.14.32",
 "hyper-rustls 0.24.2",
 "hyper-tls",
 "ipnet",
 "js-sys",
 "log",
 "mime",
 "native-tls",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "rustls 0.21.12",
 "rustls-native-certs 0.6.3",
 "rustls-pemfile 1.0.4",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper 0.1.2",
 "system-configuration 0.5.1",
 "tokio",
 "tokio-native-tls",
 "tokio-rustls 0.24.1",
 "tower-service",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "winreg 0.50.0",
]

[[package]]
name = "reqwest"
version = "0.12.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d19c46a6fdd48bc4dab94b6103fccc55d34c67cc0ad04653aad4ea2a07cd7bbb"
dependencies = [
 "base64 0.22.1",
 "bytes 1.10.1",
 "futures-channel",
 "futures-core",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-util",
 "ipnet",
 "js-sys",
 "log",
 "mime",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper 1.0.2",
 "tokio",
 "tower 0.5.2",
 "tower-service",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "windows-registry 0.4.0",
]

[[package]]
name = "reqwest"
version = "0.12.15"
source = "git+https://github.com/zed-industries/reqwest.git?rev=951c770a32f1998d6e999cef3e59e0013e6c4415#951c770a32f1998d6e999cef3e59e0013e6c4415"
dependencies = [
 "base64 0.22.1",
 "bytes 1.10.1",
 "encoding_rs",
 "futures-core",
 "futures-util",
 "h2 0.4.9",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-rustls 0.27.5",
 "hyper-util",
 "ipnet",
 "js-sys",
 "log",
 "mime",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "quinn",
 "rustls 0.23.26",
 "rustls-native-certs 0.8.1",
 "rustls-pemfile 2.2.0",
 "rustls-pki-types",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper 1.0.2",
 "system-configuration 0.6.1",
 "tokio",
 "tokio-rustls 0.26.2",
 "tokio-socks",
 "tokio-util",
 "tower 0.5.2",
 "tower-service",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "wasm-streams",
 "web-sys",
 "windows-registry 0.4.0",
]

[[package]]
name = "reqwest_client"
version = "0.1.0"
dependencies = [
 "anyhow",
 "bytes 1.10.1",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "http_client_tls",
 "log",
 "regex",
 "reqwest 0.12.15 (git+https://github.com/zed-industries/reqwest.git?rev=951c770a32f1998d6e999cef3e59e0013e6c4415)",
 "serde",
 "smol",
 "tokio",
 "workspace-hack",
]

[[package]]
name = "resvg"
version = "0.45.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8928798c0a55e03c9ca6c4c6846f76377427d2c1e1f7e6de3c06ae57942df43"
dependencies = [
 "log",
 "pico-args",
 "rgb",
 "svgtypes",
 "tiny-skia",
 "usvg",
]

[[package]]
name = "rfc6979"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7743f17af12fa0b03b803ba12cd6a8d9483a587e89c69445e3909655c0b9fabb"
dependencies = [
 "crypto-bigint 0.4.9",
 "hmac",
 "zeroize",
]

[[package]]
name = "rgb"
version = "0.8.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57397d16646700483b67d2dd6511d79318f9d057fdbd21a4066aeac8b41d310a"
dependencies = [
 "bytemuck",
]

[[package]]
name = "rich_text"
version = "0.1.0"
dependencies = [
 "futures 0.3.31",
 "gpui",
 "language",
 "linkify",
 "pulldown-cmark 0.12.2",
 "theme",
 "ui",
 "util",
 "workspace-hack",
]

[[package]]
name = "ring"
version = "0.17.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4689e6c2294d81e88dc6261c768b63bc4fcdb852be6d1352498b114f61383b7"
dependencies = [
 "cc",
 "cfg-if",
 "getrandom 0.2.15",
 "libc",
 "untrusted",
 "windows-sys 0.52.0",
]

[[package]]
name = "rkyv"
version = "0.7.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9008cd6385b9e161d8229e1f6549dd23c3d022f132a2ea37ac3a10ac4935779b"
dependencies = [
 "bitvec",
 "bytecheck",
 "bytes 1.10.1",
 "hashbrown 0.12.3",
 "ptr_meta",
 "rend",
 "rkyv_derive",
 "seahash",
 "tinyvec",
 "uuid",
]

[[package]]
name = "rkyv_derive"
version = "0.7.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "503d1d27590a2b0a3a4ca4c94755aa2875657196ecbf401a42eff41d7de532c0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "rmp"
version = "0.8.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "228ed7c16fa39782c3b3468e974aec2795e9089153cd08ee2e9aefb3613334c4"
dependencies = [
 "byteorder",
 "num-traits",
 "paste",
]

[[package]]
name = "rmpv"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58450723cd9ee93273ce44a20b6ec4efe17f8ed2e3631474387bfdecf18bb2a9"
dependencies = [
 "num-traits",
 "rmp",
]

[[package]]
name = "rodio"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7ceb6607dd738c99bc8cb28eff249b7cd5c8ec88b9db96c0608c1480d140fb1"
dependencies = [
 "cpal",
 "hound",
]

[[package]]
name = "rope"
version = "0.1.0"
dependencies = [
 "arrayvec",
 "criterion",
 "ctor",
 "gpui",
 "log",
 "rand 0.8.5",
 "rayon",
 "smallvec",
 "sum_tree",
 "unicode-segmentation",
 "util",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "roxmltree"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c20b6793b5c2fa6553b250154b78d6d0db37e72700ae35fad9387a46f487c97"

[[package]]
name = "rpc"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-tungstenite",
 "base64 0.22.1",
 "chrono",
 "collections",
 "futures 0.3.31",
 "gpui",
 "parking_lot",
 "proto",
 "rand 0.8.5",
 "rsa",
 "serde",
 "serde_json",
 "sha2",
 "strum 0.27.1",
 "tracing",
 "util",
 "workspace-hack",
 "zlog",
 "zstd",
]

[[package]]
name = "rsa"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78928ac1ed176a5ca1d17e578a1825f3d81ca54cf41053a592584b020cfd691b"
dependencies = [
 "const-oid",
 "digest",
 "num-bigint-dig",
 "num-integer",
 "num-traits",
 "pkcs1",
 "pkcs8 0.10.2",
 "rand_core 0.6.4",
 "signature 2.2.0",
 "spki 0.7.3",
 "subtle",
 "zeroize",
]

[[package]]
name = "rules_library"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "editor",
 "gpui",
 "language",
 "language_model",
 "log",
 "menu",
 "picker",
 "prompt_store",
 "release_channel",
 "rope",
 "serde",
 "settings",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "runtimelib"
version = "0.25.0"
source = "git+https://github.com/ConradIrwin/runtimed?rev=7130c804216b6914355d15d0b91ea91f6babd734#7130c804216b6914355d15d0b91ea91f6babd734"
dependencies = [
 "anyhow",
 "async-dispatcher",
 "async-std",
 "base64 0.22.1",
 "bytes 1.10.1",
 "chrono",
 "data-encoding",
 "dirs 5.0.1",
 "futures 0.3.31",
 "glob",
 "jupyter-protocol",
 "ring",
 "serde",
 "serde_json",
 "shellexpand 3.1.1",
 "smol",
 "uuid",
 "zeromq",
]

[[package]]
name = "rust-embed"
version = "8.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5fbc0ee50fcb99af7cebb442e5df7b5b45e9460ffa3f8f549cd26b862bec49d"
dependencies = [
 "rust-embed-impl",
 "rust-embed-utils",
 "walkdir",
]

[[package]]
name = "rust-embed-impl"
version = "8.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bf418c9a2e3f6663ca38b8a7134cc2c2167c9d69688860e8961e3faa731702e"
dependencies = [
 "proc-macro2",
 "quote",
 "rust-embed-utils",
 "syn 2.0.101",
 "walkdir",
]

[[package]]
name = "rust-embed-utils"
version = "8.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d55b95147fe01265d06b3955db798bdaed52e60e2211c41137701b3aba8e21"
dependencies = [
 "globset",
 "sha2",
 "walkdir",
]

[[package]]
name = "rust_decimal"
version = "1.37.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faa7de2ba56ac291bd90c6b9bece784a52ae1411f9506544b3eae36dd2356d50"
dependencies = [
 "arrayvec",
 "borsh",
 "bytes 1.10.1",
 "num-traits",
 "rand 0.8.5",
 "rkyv",
 "serde",
 "serde_json",
]

[[package]]
name = "rustc-demangle"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "719b953e2095829ee67db738b3bfa9fa368c94900df327b3f07fe6e794d2fe1f"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"

[[package]]
name = "rustc_version"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfcb3a22ef46e85b45de6ee7e79d063319ebb6594faafcf1c225ea92ab6e9b92"
dependencies = [
 "semver",
]

[[package]]
name = "rustix"
version = "0.38.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdb5bc1ae2baa591800df16c9ca78619bf65c0488b41b96ccec5d11220d8c154"
dependencies = [
 "bitflags 2.9.0",
 "errno 0.3.11",
 "itoa",
 "libc",
 "linux-raw-sys 0.4.15",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustix"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c71e83d6afe7ff64890ec6b71d6a69bb8a610ab78ce364b3352876bb4c801266"
dependencies = [
 "bitflags 2.9.0",
 "errno 0.3.11",
 "libc",
 "linux-raw-sys 0.9.4",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustix-linux-procfs"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fc84bf7e9aa16c4f2c758f27412dc9841341e16aa682d9c7ac308fe3ee12056"
dependencies = [
 "once_cell",
 "rustix 1.0.7",
]

[[package]]
name = "rustix-openpty"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a25c3aad9fc1424eb82c88087789a7d938e1829724f3e4043163baf0d13cfc12"
dependencies = [
 "errno 0.3.11",
 "libc",
 "rustix 0.38.44",
]

[[package]]
name = "rustls"
version = "0.21.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f56a14d1f48b391359b22f731fd4bd7e43c97f3c50eee276f3aa09c94784d3e"
dependencies = [
 "log",
 "ring",
 "rustls-webpki 0.101.7",
 "sct",
]

[[package]]
name = "rustls"
version = "0.23.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df51b5869f3a441595eac5e8ff14d486ff285f7b8c0df8770e49c3b56351f0f0"
dependencies = [
 "aws-lc-rs",
 "log",
 "once_cell",
 "ring",
 "rustls-pki-types",
 "rustls-webpki 0.103.1",
 "subtle",
 "zeroize",
]

[[package]]
name = "rustls-native-certs"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9aace74cb666635c918e9c12bc0d348266037aa8eb599b5cba565709a8dff00"
dependencies = [
 "openssl-probe",
 "rustls-pemfile 1.0.4",
 "schannel",
 "security-framework 2.11.1",
]

[[package]]
name = "rustls-native-certs"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcff2dd52b58a8d98a70243663a0d234c4e2b79235637849d15913394a247d3"
dependencies = [
 "openssl-probe",
 "rustls-pki-types",
 "schannel",
 "security-framework 3.2.0",
]

[[package]]
name = "rustls-pemfile"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c74cae0a4cf6ccbbf5f359f08efdf8ee7e1dc532573bf0db71968cb56b1448c"
dependencies = [
 "base64 0.21.7",
]

[[package]]
name = "rustls-pemfile"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dce314e5fee3f39953d46bb63bb8a46d40c2f8fb7cc5a3b6cab2bde9721d6e50"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "rustls-pki-types"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "229a4a4c221013e7e1f1a043678c5cc39fe5171437c88fb47151a21e6f5b5c79"
dependencies = [
 "web-time",
 "zeroize",
]

[[package]]
name = "rustls-platform-verifier"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a5467026f437b4cb2a533865eaa73eb840019a0916f4b9ec563c6e617e086c9"
dependencies = [
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "jni",
 "log",
 "once_cell",
 "rustls 0.23.26",
 "rustls-native-certs 0.8.1",
 "rustls-platform-verifier-android",
 "rustls-webpki 0.103.1",
 "security-framework 3.2.0",
 "security-framework-sys",
 "webpki-root-certs",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustls-platform-verifier-android"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f87165f0995f63a9fbeea62b64d10b4d9d8e78ec6d7d51fb2125fda7bb36788f"

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring",
 "untrusted",
]

[[package]]
name = "rustls-webpki"
version = "0.103.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fef8b8769aaccf73098557a87cd1816b4f9c7c16811c9c77142aa695c16f2c03"
dependencies = [
 "aws-lc-rs",
 "ring",
 "rustls-pki-types",
 "untrusted",
]

[[package]]
name = "rustversion"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eded382c5f5f786b989652c49544c4877d9f015cc22e145a5ea8ea66c2921cd2"

[[package]]
name = "rustybuzz"
version = "0.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfb9cf8877777222e4a3bc7eb247e398b56baba500c38c1c46842431adc8b55c"
dependencies = [
 "bitflags 2.9.0",
 "bytemuck",
 "libm",
 "smallvec",
 "ttf-parser 0.21.1",
 "unicode-bidi-mirroring 0.2.0",
 "unicode-ccc 0.2.0",
 "unicode-properties",
 "unicode-script",
]

[[package]]
name = "rustybuzz"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd3c7c96f8a08ee34eff8857b11b49b07d71d1c3f4e88f8a88d4c9e9f90b1702"
dependencies = [
 "bitflags 2.9.0",
 "bytemuck",
 "core_maths",
 "log",
 "smallvec",
 "ttf-parser 0.25.1",
 "unicode-bidi-mirroring 0.4.0",
 "unicode-ccc 0.4.0",
 "unicode-properties",
 "unicode-script",
]

[[package]]
name = "ryu"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28d3b2b1366ec20994f1fd18c3c594f05c5dd4bc44d8bb0c1c632c8d6829481f"

[[package]]
name = "salsa20"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97a22f5af31f73a954c10289c93e8a50cc23d971e80ee446f1f6f7137a088213"
dependencies = [
 "cipher",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "scap"
version = "0.0.8"
source = "git+https://github.com/zed-industries/scap?rev=08f0a01417505cc0990b9931a37e5120db92e0d0#08f0a01417505cc0990b9931a37e5120db92e0d0"
dependencies = [
 "anyhow",
 "cocoa 0.25.0",
 "core-graphics-helmer-fork",
 "log",
 "objc",
 "rand 0.8.5",
 "screencapturekit",
 "screencapturekit-sys",
 "sysinfo",
 "tao-core-video-sys",
 "windows 0.61.1",
 "windows-capture",
 "x11",
 "xcb",
]

[[package]]
name = "schannel"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f29ebaa345f945cec9fbbc532eb307f0fdad8161f281b6369539c8d84876b3d"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "schema_generator"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "env_logger 0.11.8",
 "schemars",
 "serde",
 "serde_json",
 "theme",
 "workspace-hack",
]

[[package]]
name = "schemars"
version = "0.8.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fbf2ae1b8bc8e02df939598064d22402220cd5bbcca1c76f7d6a310974d5615"
dependencies = [
 "dyn-clone",
 "indexmap",
 "schemars_derive",
 "serde",
 "serde_json",
]

[[package]]
name = "schemars_derive"
version = "0.8.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32e265784ad618884abaea0600a9adf15393368d840e0222d101a072f3f7534d"
dependencies = [
 "proc-macro2",
 "quote",
 "serde_derive_internals",
 "syn 2.0.101",
]

[[package]]
name = "scoped-tls"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1cf6437eb19a8f4a6cc0f7dca544973b0b78843adbfeb3683d1a94a0024a294"

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "scratch"
version = "1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f6280af86e5f559536da57a45ebc84948833b3bee313a7dd25232e09c878a52"

[[package]]
name = "screencapturekit"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a5eeeb57ac94960cfe5ff4c402be6585ae4c8d29a2cf41b276048c2e849d64e"
dependencies = [
 "screencapturekit-sys",
]

[[package]]
name = "screencapturekit-sys"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22411b57f7d49e7fe08025198813ee6fd65e1ee5eff4ebc7880c12c82bde4c60"
dependencies = [
 "block",
 "dispatch",
 "objc",
 "objc-foundation",
 "objc_id",
 "once_cell",
]

[[package]]
name = "scrypt"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0516a385866c09368f0b5bcd1caff3366aace790fcd46e2bb032697bb172fd1f"
dependencies = [
 "password-hash 0.5.0",
 "pbkdf2 0.12.2",
 "salsa20",
 "sha2",
]

[[package]]
name = "sct"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da046153aa2352493d6cb7da4b6e5c0c057d8a1d0a9aa8560baffdd945acd414"
dependencies = [
 "ring",
 "untrusted",
]

[[package]]
name = "sea-bae"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f694a6ab48f14bc063cfadff30ab551d3c7e46d8f81836c51989d548f44a2a25"
dependencies = [
 "heck 0.4.1",
 "proc-macro-error2",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sea-orm"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21e61af841881c137d4bc8e0d8411cee9168548b404f9e4788e8af7e8f94bd4e"
dependencies = [
 "async-stream",
 "async-trait",
 "bigdecimal",
 "chrono",
 "futures-util",
 "log",
 "ouroboros",
 "pgvector",
 "rust_decimal",
 "sea-orm-macros",
 "sea-query",
 "sea-query-binder",
 "serde",
 "serde_json",
 "sqlx",
 "strum 0.26.3",
 "thiserror 2.0.12",
 "time",
 "tracing",
 "url",
 "uuid",
]

[[package]]
name = "sea-orm-macros"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6b86e3e77b548e6c6c1f612a1ca024d557dffdb81b838bf482ad3222140c77b"
dependencies = [
 "heck 0.4.1",
 "proc-macro2",
 "quote",
 "sea-bae",
 "syn 2.0.101",
 "unicode-ident",
]

[[package]]
name = "sea-query"
version = "0.32.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d99447c24da0cded00089e2021e1624af90878c65f7534319448d01da3df869d"
dependencies = [
 "bigdecimal",
 "chrono",
 "inherent",
 "ordered-float 4.6.0",
 "rust_decimal",
 "serde_json",
 "time",
 "uuid",
]

[[package]]
name = "sea-query-binder"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0019f47430f7995af63deda77e238c17323359af241233ec768aba1faea7608"
dependencies = [
 "bigdecimal",
 "chrono",
 "rust_decimal",
 "sea-query",
 "serde_json",
 "sqlx",
 "time",
 "uuid",
]

[[package]]
name = "seahash"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c107b6f4780854c8b126e228ea8869f4d7b71260f962fefb57b996b8959ba6b"

[[package]]
name = "search"
version = "0.1.0"
dependencies = [
 "any_vec",
 "anyhow",
 "bitflags 2.9.0",
 "client",
 "collections",
 "editor",
 "futures 0.3.31",
 "gpui",
 "language",
 "menu",
 "project",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "theme",
 "ui",
 "unindent",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "sec1"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3be24c1842290c45df0a7bf069e0c268a747ad05a192f2fd7dcfdbc1cba40928"
dependencies = [
 "base16ct",
 "der 0.6.1",
 "generic-array",
 "pkcs8 0.9.0",
 "subtle",
 "zeroize",
]

[[package]]
name = "security-framework"
version = "2.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "897b2245f0b511c87893af39b033e5ca9cce68824c4d7e7630b5a1d339658d02"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.9.4",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271720403f46ca04f7ba6f55d438f8bd878d6b8ca0a1046e8228c4145bcbb316"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49db231d56a190491cb4aeda9527f1ad45345af50b0851622a7adb8c03b01c32"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "self_cell"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f7d95a54511e0c7be3f51e8867aa8cf35148d7b9445d44de2f943e2b206e749"

[[package]]
name = "semantic_index"
version = "0.1.0"
dependencies = [
 "anyhow",
 "arrayvec",
 "blake3",
 "client",
 "clock",
 "collections",
 "feature_flags",
 "fs",
 "futures 0.3.31",
 "futures-batch",
 "gpui",
 "heed",
 "http_client",
 "language",
 "language_model",
 "languages",
 "log",
 "open_ai",
 "parking_lot",
 "project",
 "reqwest_client",
 "serde",
 "serde_json",
 "settings",
 "sha2",
 "smol",
 "streaming-iterator",
 "tempfile",
 "theme",
 "tree-sitter",
 "ui",
 "unindent",
 "util",
 "workspace",
 "workspace-hack",
 "worktree",
 "zlog",
]

[[package]]
name = "semantic_version"
version = "0.1.0"
dependencies = [
 "anyhow",
 "serde",
 "workspace-hack",
]

[[package]]
name = "semver"
version = "1.0.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56e6fa9c48d24d85fb3de5ad847117517440f6beceb7798af16b4a87d616b8d0"
dependencies = [
 "serde",
]

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "serde_derive_internals"
version = "0.29.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18d26a20a969b9e3fdf2fc2d9f21eda6c40e2de84c9408bb5d3b05d499aae711"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "serde_fmt"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1d4ddca14104cd60529e8c7f7ba71a2c8acd8f7f5cfcdc2faf97eeb7c3010a4"
dependencies = [
 "serde",
]

[[package]]
name = "serde_json"
version = "1.0.140"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20068b6e96dc6c9bd23e01df8827e6c7e1f2fddd43c21810382803c136b99373"
dependencies = [
 "indexmap",
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_json_lenient"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e033097bf0d2b59a62b42c18ebbb797503839b26afdda2c4e1415cb6c813540"
dependencies = [
 "indexmap",
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_path_to_error"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59fab13f937fa393d08645bf3a84bdfe86e296747b506ada67bb15f10f218b2a"
dependencies = [
 "itoa",
 "serde",
]

[[package]]
name = "serde_qs"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7715380eec75f029a4ef7de39a9200e0a63823176b759d055b613f5a87df6a6"
dependencies = [
 "percent-encoding",
 "serde",
 "thiserror 1.0.69",
]

[[package]]
name = "serde_qs"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cac3f1e2ca2fe333923a1ae72caca910b98ed0630bb35ef6f8c8517d6e81afa"
dependencies = [
 "percent-encoding",
 "serde",
 "thiserror 1.0.69",
]

[[package]]
name = "serde_repr"
version = "0.1.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "175ee3e80ae9982737ca543e96133087cbd9a485eecc3bc4de9c1a37b47ea59c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "serde_spanned"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87607cb1398ed59d48732e575a4c28a7a8ebf2454b964fe3f224f2afc07909e1"
dependencies = [
 "serde",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3491c14715ca2294c4d6a88f15e84739788c1d030eed8c110436aafdaa2f3fd"
dependencies = [
 "form_urlencoded",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serial2"
version = "0.2.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7d1d08630509d69f90eff4afcd02c3bd974d979225cbd815ff5942351b14375"
dependencies = [
 "cfg-if",
 "libc",
 "winapi",
]

[[package]]
name = "session"
version = "0.1.0"
dependencies = [
 "db",
 "gpui",
 "serde_json",
 "util",
 "uuid",
 "workspace-hack",
]

[[package]]
name = "settings"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "ec4rs",
 "fs",
 "futures 0.3.31",
 "gpui",
 "indoc",
 "inventory",
 "log",
 "paths",
 "pretty_assertions",
 "release_channel",
 "rust-embed",
 "schemars",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_json_lenient",
 "smallvec",
 "streaming-iterator",
 "tree-sitter",
 "tree-sitter-json",
 "unindent",
 "util",
 "workspace-hack",
]

[[package]]
name = "settings_ui"
version = "0.1.0"
dependencies = [
 "command_palette_hooks",
 "editor",
 "feature_flags",
 "fs",
 "gpui",
 "log",
 "paths",
 "schemars",
 "serde",
 "settings",
 "theme",
 "ui",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest",
]

[[package]]
name = "sha1-checked"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89f599ac0c323ebb1c6082821a54962b839832b03984598375bff3975b804423"
dependencies = [
 "digest",
 "sha1",
]

[[package]]
name = "sha1_smol"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbfa15b3dddfee50a0fff136974b3e1bde555604ba463834a7eb7deb6417705d"

[[package]]
name = "sha2"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793db75ad2bcafc3ffa7c68b215fee268f537982cd901d132f89c6343f3a3dc8"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shared_library"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a9e7e0f2bfae24d8a5b5a66c5b257a83c7412304311512a0c054cd5e619da11"
dependencies = [
 "lazy_static",
 "libc",
]

[[package]]
name = "shell-words"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24188a676b6ae68c3b2cb3a01be17fbf7240ce009799bb56d5b1409051e78fde"

[[package]]
name = "shellexpand"
version = "2.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ccc8076840c4da029af4f87e4e8daeb0fca6b87bbb02e10cb60b791450e11e4"
dependencies = [
 "dirs 4.0.0",
]

[[package]]
name = "shellexpand"
version = "3.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b1fdf65dd6331831494dd616b30351c38e96e45921a27745cf98490458b90bb"
dependencies = [
 "dirs 6.0.0",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "signal-hook"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8621587d4798caf8eb44879d42e56b9a93ea5dcd315a6487c357130095b62801"
dependencies = [
 "libc",
 "signal-hook-registry",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9203b8055f63a2a00e2f593bb0510367fe707d7ff1e5c872de2f537b339e5410"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "1.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74233d3b3b2f6d4b006dc19dee745e73e2a6bfb6f93607cd3b02bd5b00797d7c"
dependencies = [
 "digest",
 "rand_core 0.6.4",
]

[[package]]
name = "signature"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77549399552de45a898a580c1b41d445bf730df867cc44e6c0233bbc4b8329de"
dependencies = [
 "digest",
 "rand_core 0.6.4",
]

[[package]]
name = "simd-adler32"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d66dc143e6b11c1eddc06d5c423cfc97062865baf299914ab64caa38182078fe"

[[package]]
name = "simd_helpers"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95890f873bec569a0362c235787f3aca6e1e887302ba4840839bcc6459c42da6"
dependencies = [
 "quote",
]

[[package]]
name = "simdutf8"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3a9fe34e3e7a50316060351f37187a3f546bce95496156754b601a5fa71b76e"

[[package]]
name = "simple_asn1"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "297f631f50729c8c99b84667867963997ec0b50f32b2a7dbcab828ef0541e8bb"
dependencies = [
 "num-bigint",
 "num-traits",
 "thiserror 2.0.12",
 "time",
]

[[package]]
name = "simplecss"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a9c6883ca9c3c7c90e888de77b7a5c849c779d25d74a1269b0218b14e8b136c"
dependencies = [
 "log",
]

[[package]]
name = "simplelog"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16257adbfaef1ee58b1363bdc0664c9b8e1e30aed86049635fb5f147d065a9c0"
dependencies = [
 "log",
 "termcolor",
 "time",
]

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"

[[package]]
name = "skrifa"
version = "0.26.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cc1aa86c26dbb1b63875a7180aa0819709b33348eb5b1491e4321fae388179d"
dependencies = [
 "bytemuck",
 "read-fonts",
]

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "slash_commands_example"
version = "0.1.0"
dependencies = [
 "zed_extension_api 0.1.0",
]

[[package]]
name = "slotmap"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbff4acf519f630b3a3ddcfaea6c06b42174d9a44bc70c620e9ed1649d58b82a"
dependencies = [
 "version_check",
]

[[package]]
name = "smallvec"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8917285742e9f3e1683f0a9c4e6b57960b7314d0b08d30d1ecd426713ee2eee9"
dependencies = [
 "serde",
]

[[package]]
name = "smart-default"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "133659a15339456eeeb07572eb02a91c91e9815e9cbc89566944d2c8d3efdbf6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "smol"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a33bd3e260892199c3ccfc487c88b2da2265080acb316cd920da72fdfd7c599f"
dependencies = [
 "async-channel 2.3.1",
 "async-executor",
 "async-fs",
 "async-io",
 "async-lock",
 "async-net",
 "async-process",
 "blocking",
 "futures-lite 2.6.0",
]

[[package]]
name = "smol_str"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fad6c857cbab2627dcf01ec85a623ca4e7dcb5691cbaa3d7fb7653671f0d09c9"
dependencies = [
 "serde",
]

[[package]]
name = "smol_str"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd538fb6910ac1099850255cf94a94df6551fbdd602454387d0adb2d1ca6dead"

[[package]]
name = "snippet"
version = "0.1.0"
dependencies = [
 "anyhow",
 "smallvec",
 "workspace-hack",
]

[[package]]
name = "snippet_provider"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "extension",
 "fs",
 "futures 0.3.31",
 "gpui",
 "indoc",
 "parking_lot",
 "paths",
 "schemars",
 "serde",
 "serde_json_lenient",
 "snippet",
 "util",
 "workspace-hack",
]

[[package]]
name = "snippets_ui"
version = "0.1.0"
dependencies = [
 "file_finder",
 "file_icons",
 "fuzzy",
 "gpui",
 "language",
 "paths",
 "picker",
 "settings",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "socket2"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f5fd57c80058a56cf5c777ab8a126398ece8e442983605d280a44ce79d0edef"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "spdx"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58b69356da67e2fc1f542c71ea7e654a361a79c938e4424392ecf4fa065d2193"
dependencies = [
 "smallvec",
]

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"
dependencies = [
 "lock_api",
]

[[package]]
name = "spirv"
version = "0.3.0+sdk-1.3.268.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eda41003dc44290527a59b13432d4a0379379fa074b70174882adfbdfd917844"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "spki"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67cf02bbac7a337dc36e4f5a693db6c21e7863f45070f7064577eb4367a3212b"
dependencies = [
 "base64ct",
 "der 0.6.1",
]

[[package]]
name = "spki"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d91ed6c858b01f942cd56b37a94b3e0a1798290327d1236e4d9cf4eaca44d29d"
dependencies = [
 "base64ct",
 "der 0.7.10",
]

[[package]]
name = "sptr"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b9b39299b249ad65f3b7e96443bad61c02ca5cd3589f46cb6d610a0fd6c0d6a"

[[package]]
name = "sqlez"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "futures 0.3.31",
 "indoc",
 "libsqlite3-sys",
 "parking_lot",
 "smol",
 "sqlformat",
 "thread_local",
 "util",
 "uuid",
 "workspace-hack",
]

[[package]]
name = "sqlez_macros"
version = "0.1.0"
dependencies = [
 "sqlez",
 "sqlformat",
 "syn 2.0.101",
 "workspace-hack",
]

[[package]]
name = "sqlformat"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bba3a93db0cc4f7bdece8bb09e77e2e785c20bfebf79eb8340ed80708048790"
dependencies = [
 "nom",
 "unicode_categories",
]

[[package]]
name = "sqlx"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3c3a85280daca669cfd3bcb68a337882a8bc57ec882f72c5d13a430613a738e"
dependencies = [
 "sqlx-core",
 "sqlx-macros",
 "sqlx-mysql",
 "sqlx-postgres",
 "sqlx-sqlite",
]

[[package]]
name = "sqlx-core"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f743f2a3cea30a58cd479013f75550e879009e3a02f616f18ca699335aa248c3"
dependencies = [
 "base64 0.22.1",
 "bigdecimal",
 "bytes 1.10.1",
 "chrono",
 "crc",
 "crossbeam-queue",
 "either",
 "event-listener 5.4.0",
 "futures-core",
 "futures-intrusive",
 "futures-io",
 "futures-util",
 "hashbrown 0.15.3",
 "hashlink 0.10.0",
 "indexmap",
 "log",
 "memchr",
 "once_cell",
 "percent-encoding",
 "rust_decimal",
 "rustls 0.23.26",
 "serde",
 "serde_json",
 "sha2",
 "smallvec",
 "thiserror 2.0.12",
 "time",
 "tokio",
 "tokio-stream",
 "tracing",
 "url",
 "uuid",
 "webpki-roots",
]

[[package]]
name = "sqlx-macros"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f4200e0fde19834956d4252347c12a083bdcb237d7a1a1446bffd8768417dce"
dependencies = [
 "proc-macro2",
 "quote",
 "sqlx-core",
 "sqlx-macros-core",
 "syn 2.0.101",
]

[[package]]
name = "sqlx-macros-core"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "882ceaa29cade31beca7129b6beeb05737f44f82dbe2a9806ecea5a7093d00b7"
dependencies = [
 "dotenvy",
 "either",
 "heck 0.5.0",
 "hex",
 "once_cell",
 "proc-macro2",
 "quote",
 "serde",
 "serde_json",
 "sha2",
 "sqlx-core",
 "sqlx-mysql",
 "sqlx-postgres",
 "sqlx-sqlite",
 "syn 2.0.101",
 "tempfile",
 "tokio",
 "url",
]

[[package]]
name = "sqlx-mysql"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0afdd3aa7a629683c2d750c2df343025545087081ab5942593a5288855b1b7a7"
dependencies = [
 "atoi",
 "base64 0.22.1",
 "bigdecimal",
 "bitflags 2.9.0",
 "byteorder",
 "bytes 1.10.1",
 "chrono",
 "crc",
 "digest",
 "dotenvy",
 "either",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-util",
 "generic-array",
 "hex",
 "hkdf",
 "hmac",
 "itoa",
 "log",
 "md-5",
 "memchr",
 "once_cell",
 "percent-encoding",
 "rand 0.8.5",
 "rsa",
 "rust_decimal",
 "serde",
 "sha1",
 "sha2",
 "smallvec",
 "sqlx-core",
 "stringprep",
 "thiserror 2.0.12",
 "time",
 "tracing",
 "uuid",
 "whoami",
]

[[package]]
name = "sqlx-postgres"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0bedbe1bbb5e2615ef347a5e9d8cd7680fb63e77d9dafc0f29be15e53f1ebe6"
dependencies = [
 "atoi",
 "base64 0.22.1",
 "bigdecimal",
 "bitflags 2.9.0",
 "byteorder",
 "chrono",
 "crc",
 "dotenvy",
 "etcetera",
 "futures-channel",
 "futures-core",
 "futures-util",
 "hex",
 "hkdf",
 "hmac",
 "home",
 "itoa",
 "log",
 "md-5",
 "memchr",
 "num-bigint",
 "once_cell",
 "rand 0.8.5",
 "rust_decimal",
 "serde",
 "serde_json",
 "sha2",
 "smallvec",
 "sqlx-core",
 "stringprep",
 "thiserror 2.0.12",
 "time",
 "tracing",
 "uuid",
 "whoami",
]

[[package]]
name = "sqlx-sqlite"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c26083e9a520e8eb87a06b12347679b142dc2ea29e6e409f805644a7a979a5bc"
dependencies = [
 "atoi",
 "chrono",
 "flume",
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-intrusive",
 "futures-util",
 "libsqlite3-sys",
 "log",
 "percent-encoding",
 "serde",
 "serde_urlencoded",
 "sqlx-core",
 "thiserror 2.0.12",
 "time",
 "tracing",
 "url",
 "uuid",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "story"
version = "0.1.0"
dependencies = [
 "gpui",
 "itertools 0.14.0",
 "smallvec",
 "workspace-hack",
]

[[package]]
name = "storybook"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "collab_ui",
 "ctrlc",
 "dialoguer",
 "editor",
 "fuzzy",
 "gpui",
 "indoc",
 "language",
 "log",
 "menu",
 "picker",
 "project",
 "reqwest_client",
 "rust-embed",
 "settings",
 "simplelog",
 "story",
 "strum 0.27.1",
 "theme",
 "title_bar",
 "ui",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "streaming-iterator"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b2231b7c3057d5e4ad0156fb3dc807d900806020c5ffa3ee6ff2c8c76fb8520"

[[package]]
name = "streaming_diff"
version = "0.1.0"
dependencies = [
 "ordered-float 2.10.1",
 "rand 0.8.5",
 "rope",
 "util",
 "workspace-hack",
]

[[package]]
name = "strict-num"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6637bab7722d379c8b41ba849228d680cc12d0a45ba1fa2b48f2a30577a06731"
dependencies = [
 "float-cmp",
]

[[package]]
name = "string_cache"
version = "0.8.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf776ba3fa74f83bf4b63c3dcbbf82173db2632ed8452cb2d891d33f459de70f"
dependencies = [
 "new_debug_unreachable",
 "parking_lot",
 "phf_shared",
 "precomputed-hash",
 "serde",
]

[[package]]
name = "string_cache_codegen"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c711928715f1fe0fe509c53b43e993a9a557babc2d0a3567d0a3006f1ac931a0"
dependencies = [
 "phf_generator",
 "phf_shared",
 "proc-macro2",
 "quote",
]

[[package]]
name = "stringprep"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b4df3d392d81bd458a8a621b8bffbd2302a12ffe288a9d931670948749463b1"
dependencies = [
 "unicode-bidi",
 "unicode-normalization",
 "unicode-properties",
]

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "strum"
version = "0.26.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fec0f0aef304996cf250b31b5a10dee7980c85da9d759361292b8bca5a18f06"
dependencies = [
 "strum_macros 0.26.4",
]

[[package]]
name = "strum"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f64def088c51c9510a8579e3c5d67c65349dcf755e5479ad3d010aa6454e2c32"
dependencies = [
 "strum_macros 0.27.1",
]

[[package]]
name = "strum_macros"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c6bee85a5a24955dc440386795aa378cd9cf82acd5f764469152d2270e581be"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.101",
]

[[package]]
name = "strum_macros"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c77a8c5abcaf0f9ce05d62342b7d298c346515365c36b673df4ebe3ced01fde8"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.101",
]

[[package]]
name = "subtle"
version = "2.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c2bddecc57b384dee18652358fb23172facb8a2c51ccc10d74c157bdea3292"

[[package]]
name = "sum_tree"
version = "0.1.0"
dependencies = [
 "arrayvec",
 "ctor",
 "log",
 "rand 0.8.5",
 "rayon",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "supermaven"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "collections",
 "editor",
 "env_logger 0.11.8",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "inline_completion",
 "language",
 "log",
 "postage",
 "project",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "supermaven_api",
 "text",
 "theme",
 "ui",
 "unicode-segmentation",
 "util",
 "workspace-hack",
]

[[package]]
name = "supermaven_api"
version = "0.1.0"
dependencies = [
 "anyhow",
 "futures 0.3.31",
 "http_client",
 "paths",
 "serde",
 "serde_json",
 "smol",
 "workspace-hack",
]

[[package]]
name = "sval"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cc9739f56c5d0c44a5ed45473ec868af02eb896af8c05f616673a31e1d1bb09"

[[package]]
name = "sval_buffer"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f39b07436a8c271b34dad5070c634d1d3d76d6776e938ee97b4a66a5e8003d0b"
dependencies = [
 "sval",
 "sval_ref",
]

[[package]]
name = "sval_dynamic"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffcb072d857431bf885580dacecf05ed987bac931230736739a79051dbf3499b"
dependencies = [
 "sval",
]

[[package]]
name = "sval_fmt"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f214f427ad94a553e5ca5514c95c6be84667cbc5568cce957f03f3477d03d5c"
dependencies = [
 "itoa",
 "ryu",
 "sval",
]

[[package]]
name = "sval_json"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "389ed34b32e638dec9a99c8ac92d0aa1220d40041026b625474c2b6a4d6f4feb"
dependencies = [
 "itoa",
 "ryu",
 "sval",
]

[[package]]
name = "sval_nested"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14bae8fcb2f24fee2c42c1f19037707f7c9a29a0cda936d2188d48a961c4bb2a"
dependencies = [
 "sval",
 "sval_buffer",
 "sval_ref",
]

[[package]]
name = "sval_ref"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a4eaea3821d3046dcba81d4b8489421da42961889902342691fb7eab491d79e"
dependencies = [
 "sval",
]

[[package]]
name = "sval_serde"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "172dd4aa8cb3b45c8ac8f3b4111d644cd26938b0643ede8f93070812b87fb339"
dependencies = [
 "serde",
 "sval",
 "sval_nested",
]

[[package]]
name = "svg_fmt"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0193cc4331cfd2f3d2011ef287590868599a2f33c3e69bc22c1a3d3acf9e02fb"

[[package]]
name = "svgtypes"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68c7541fff44b35860c1a7a47a7cadf3e4a304c457b58f9870d9706ece028afc"
dependencies = [
 "kurbo",
 "siphasher",
]

[[package]]
name = "swash"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fae9a562c7b46107d9c78cd78b75bbe1e991c16734c0aee8ff0ee711fb8b620a"
dependencies = [
 "skrifa",
 "yazi",
 "zeno",
]

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.101"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ce2b7fc941b3a24138a0a7cf8e858bfc6a992e7978a068a5c760deb0ed43caf"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "sync_wrapper"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2047c6ded9c721764247e62cd3b03c09ffc529b2ba5b10ec482ae507a4a70160"

[[package]]
name = "sync_wrapper"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bf256ce5efdfa370213c1dabab5935a12e49f2c58d15e9eac2870d3b4f27263"
dependencies = [
 "futures-core",
]

[[package]]
name = "synchronoise"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3dbc01390fc626ce8d1cffe3376ded2b72a11bb70e1c75f404a210e4daa4def2"
dependencies = [
 "crossbeam-queue",
]

[[package]]
name = "synstructure"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8af7666ab7b6390ab78131fb5b0fce11d6b7a6951602017c35fa82800708971"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sys-locale"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8eab9a99a024a169fe8a903cf9d4a3b3601109bcc13bd9e3c6fff259138626c4"
dependencies = [
 "libc",
]

[[package]]
name = "sysinfo"
version = "0.31.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "355dbe4f8799b304b05e1b0f05fc59b2a18d36645cf169607da45bde2f69a1be"
dependencies = [
 "core-foundation-sys",
 "libc",
 "memchr",
 "ntapi",
 "rayon",
 "windows 0.57.0",
]

[[package]]
name = "system-configuration"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba3a3adc5c275d719af8cb4272ea1c4a6d668a777f37e115f6d11ddbc1c8e0e7"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation 0.9.4",
 "system-configuration-sys 0.5.0",
]

[[package]]
name = "system-configuration"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c879d448e9d986b661742763247d3693ed13609438cf3d006f51f5368a5ba6b"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.9.4",
 "system-configuration-sys 0.6.0",
]

[[package]]
name = "system-configuration-sys"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75fb188eb626b924683e3b95e3a48e63551fcfb51949de2f06a9d91dbee93c9"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "system-configuration-sys"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e1d1b10ced5ca923a1fcb8d03e96b8d3268065d724548c0211415ff6ac6bac4"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "system-deps"
version = "6.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3e535eb8dded36d55ec13eddacd30dec501792ff23a0b1682c38601b8cf2349"
dependencies = [
 "cfg-expr",
 "heck 0.5.0",
 "pkg-config",
 "toml 0.8.20",
 "version-compare",
]

[[package]]
name = "system-interface"
version = "0.27.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc4592f674ce18521c2a81483873a49596655b179f71c5e05d10c1fe66c78745"
dependencies = [
 "bitflags 2.9.0",
 "cap-fs-ext",
 "cap-std",
 "fd-lock",
 "io-lifetimes",
 "rustix 0.38.44",
 "windows-sys 0.59.0",
 "winx",
]

[[package]]
name = "tab_switcher"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "ctor",
 "editor",
 "fuzzy",
 "gpui",
 "language",
 "menu",
 "picker",
 "project",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smol",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "taffy"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ec17858c2d465b2f734b798b920818a974faf0babb15d7fef81818a4b2d16f1"
dependencies = [
 "arrayvec",
 "grid",
 "num-traits",
 "serde",
 "slotmap",
]

[[package]]
name = "tagptr"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b2093cf4c8eb1e67749a6762251bc9cd836b6fc171623bd0a9d324d37af2417"

[[package]]
name = "take-until"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bdb6fa0dfa67b38c1e66b7041ba9dcf23b99d8121907cd31c807a332f7a0bbb"

[[package]]
name = "tao-core-video-sys"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271450eb289cb4d8d0720c6ce70c72c8c858c93dd61fc625881616752e6b98f6"
dependencies = [
 "cfg-if",
 "core-foundation-sys",
 "libc",
 "objc",
]

[[package]]
name = "tap"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55937e1799185b12863d447f42597ed69d9928686b8d88a1df17376a097d8369"

[[package]]
name = "target-lexicon"
version = "0.12.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61c41af27dd6d1e27b1b16b489db798443478cef1f06a660c96db617ba5de3b1"

[[package]]
name = "target-lexicon"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e502f78cdbb8ba4718f566c418c52bc729126ffd16baee5baa718cf25dd5a69a"

[[package]]
name = "task"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "futures 0.3.31",
 "gpui",
 "hex",
 "log",
 "parking_lot",
 "pretty_assertions",
 "proto",
 "schemars",
 "serde",
 "serde_json",
 "serde_json_lenient",
 "sha2",
 "shellexpand 2.1.2",
 "util",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "tasks_ui"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "editor",
 "file_icons",
 "fuzzy",
 "gpui",
 "itertools 0.14.0",
 "language",
 "menu",
 "picker",
 "project",
 "serde",
 "serde_json",
 "task",
 "tree-sitter-rust",
 "tree-sitter-typescript",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "telemetry"
version = "0.1.0"
dependencies = [
 "futures 0.3.31",
 "serde",
 "serde_json",
 "telemetry_events",
 "workspace-hack",
]

[[package]]
name = "telemetry_events"
version = "0.1.0"
dependencies = [
 "semantic_version",
 "serde",
 "serde_json",
 "workspace-hack",
]

[[package]]
name = "tempfile"
version = "3.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a64e3985349f2441a1a9ef0b853f869006c3855f2cda6862a94d26ebb9d6a1"
dependencies = [
 "fastrand 2.3.0",
 "getrandom 0.3.2",
 "once_cell",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "tendril"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d24a120c5fc464a3458240ee02c299ebcb9d67b5249c8848b09d639dca8d7bb0"
dependencies = [
 "futf",
 "mac",
 "utf-8",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "terminal"
version = "0.1.0"
dependencies = [
 "alacritty_terminal",
 "anyhow",
 "collections",
 "dirs 4.0.0",
 "futures 0.3.31",
 "gpui",
 "libc",
 "rand 0.8.5",
 "regex",
 "release_channel",
 "schemars",
 "serde",
 "serde_derive",
 "settings",
 "smol",
 "sysinfo",
 "task",
 "theme",
 "thiserror 2.0.12",
 "url",
 "util",
 "windows 0.61.1",
 "workspace-hack",
]

[[package]]
name = "terminal_size"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45c6481c4829e4cc63825e62c49186a34538b7b2750b73b266581ffb612fb5ed"
dependencies = [
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "terminal_view"
version = "0.1.0"
dependencies = [
 "anyhow",
 "assistant_slash_command",
 "async-recursion 1.1.1",
 "breadcrumbs",
 "client",
 "collections",
 "db",
 "dirs 4.0.0",
 "editor",
 "futures 0.3.31",
 "gpui",
 "itertools 0.14.0",
 "language",
 "log",
 "project",
 "rand 0.8.5",
 "regex",
 "schemars",
 "search",
 "serde",
 "serde_json",
 "settings",
 "shellexpand 2.1.2",
 "smol",
 "task",
 "terminal",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "text"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clock",
 "collections",
 "ctor",
 "gpui",
 "http_client",
 "log",
 "parking_lot",
 "postage",
 "rand 0.8.5",
 "regex",
 "rope",
 "smallvec",
 "sum_tree",
 "util",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "theme"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "derive_more",
 "fs",
 "futures 0.3.31",
 "gpui",
 "indexmap",
 "log",
 "palette",
 "parking_lot",
 "refineable",
 "schemars",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_json_lenient",
 "serde_repr",
 "settings",
 "strum 0.27.1",
 "thiserror 2.0.12",
 "util",
 "uuid",
 "workspace-hack",
]

[[package]]
name = "theme_extension"
version = "0.1.0"
dependencies = [
 "anyhow",
 "extension",
 "fs",
 "gpui",
 "theme",
 "workspace-hack",
]

[[package]]
name = "theme_importer"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "gpui",
 "indexmap",
 "log",
 "palette",
 "rust-embed",
 "serde",
 "serde_json",
 "serde_json_lenient",
 "simplelog",
 "strum 0.27.1",
 "theme",
 "vscode_theme",
 "workspace-hack",
]

[[package]]
name = "theme_selector"
version = "0.1.0"
dependencies = [
 "fs",
 "fuzzy",
 "gpui",
 "log",
 "picker",
 "serde",
 "settings",
 "telemetry",
 "theme",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "thiserror"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6aaf5339b578ea85b50e080feb250a3e8ae8cfcdff9a461c9ec2904bc923f52"
dependencies = [
 "thiserror-impl 1.0.69",
]

[[package]]
name = "thiserror"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567b8a2dae586314f7be2a752ec7474332959c6460e02bde30d702a66d488708"
dependencies = [
 "thiserror-impl 2.0.12",
]

[[package]]
name = "thiserror-impl"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fee6c4efc90059e10f81e6d42c60a18f76588c3d74cb83a0b242a2b6c7504c1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "thiserror-impl"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7cf42b4507d8ea322120659672cf1b9dbb93f8f2d4ecfd6e51350ff5b17a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "thread_local"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9ef9bad013ada3808854ceac7b46812a6465ba368859a37e2100283d2d719c"
dependencies = [
 "cfg-if",
 "once_cell",
]

[[package]]
name = "tiff"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba1310fcea54c6a9a4fd1aad794ecc02c31682f6bfbecdf460bf19533eed1e3e"
dependencies = [
 "flate2",
 "jpeg-decoder",
 "weezl",
]

[[package]]
name = "tiktoken-rs"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25563eeba904d770acf527e8b370fe9a5547bacd20ff84a0b6c3bc41288e5625"
dependencies = [
 "anyhow",
 "base64 0.22.1",
 "bstr",
 "fancy-regex 0.13.0",
 "lazy_static",
 "regex",
 "rustc-hash 1.1.0",
]

[[package]]
name = "time"
version = "0.3.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a7619e19bc266e0f9c5e6686659d394bc57973859340060a69221e57dbc0c40"
dependencies = [
 "deranged",
 "itoa",
 "libc",
 "num-conv",
 "num_threads",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9e9a38711f559d9e3ce1cdb06dd7c5b8ea546bc90052da6d06bb76da74bb07c"

[[package]]
name = "time-macros"
version = "0.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3526739392ec93fd8b359c8e98514cb3e8e021beb4e5f597b00a0221f8ed8a49"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "time_format"
version = "0.1.0"
dependencies = [
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "sys-locale",
 "time",
 "workspace-hack",
]

[[package]]
name = "tiny-keccak"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c9d3793400a45f954c52e73d068316d76b6f4e36977e3fcebb13a2721e80237"
dependencies = [
 "crunchy",
]

[[package]]
name = "tiny-skia"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83d13394d44dae3207b52a326c0c85a8bf87f1541f23b0d143811088497b09ab"
dependencies = [
 "arrayref",
 "arrayvec",
 "bytemuck",
 "cfg-if",
 "log",
 "png",
 "tiny-skia-path",
]

[[package]]
name = "tiny-skia-path"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c9e7fc0c2e86a30b117d0462aa261b72b7a99b7ebd7deb3a14ceda95c5bdc93"
dependencies = [
 "arrayref",
 "bytemuck",
 "strict-num",
]

[[package]]
name = "tiny_http"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce51b50006056f590c9b7c3808c3bd70f0d1101666629713866c227d6e58d39"
dependencies = [
 "ascii",
 "chrono",
 "chunked_transfer",
 "log",
 "url",
]

[[package]]
name = "tinystr"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9117f5d4db391c1cf6927e7bea3db74b9a1c1add8f7eda9ffd5364f40f57b82f"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "tinytemplate"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be4d6b5f19ff7664e8c98d03e2139cb510db9b0a60b55f8e8709b689d939b6bc"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "tinyvec"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09b3661f17e86524eccd4371ab0429194e0d7c008abb45f7a7495b1719463c71"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "title_bar"
version = "0.1.0"
dependencies = [
 "auto_update",
 "call",
 "chrono",
 "client",
 "collections",
 "db",
 "gpui",
 "http_client",
 "notifications",
 "pretty_assertions",
 "project",
 "remote",
 "rpc",
 "schemars",
 "serde",
 "settings",
 "smallvec",
 "story",
 "telemetry",
 "theme",
 "tree-sitter-md",
 "ui",
 "util",
 "windows 0.61.1",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "tokio"
version = "1.44.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6b88822cbe49de4185e3a4cbf8321dd487cf5fe0c5c65695fef6346371e9c48"
dependencies = [
 "backtrace",
 "bytes 1.10.1",
 "libc",
 "mio",
 "parking_lot",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2",
 "tokio-macros",
 "windows-sys 0.52.0",
]

[[package]]
name = "tokio-io"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57fc868aae093479e3131e3d165c93b1c7474109d13c90ec0dda2a1bbfff0674"
dependencies = [
 "bytes 0.4.12",
 "futures 0.1.31",
 "log",
]

[[package]]
name = "tokio-macros"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e06d43f1345a3bcd39f6a56dbb7dcab2ba47e68e8ac134855e7e2bdbaf8cab8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tokio-native-tls"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbae76ab933c85776efabc971569dd6119c580d8f5d448769dec1764bf796ef2"
dependencies = [
 "native-tls",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28327cf380ac148141087fbfb9de9d7bd4e84ab5d2c28fbc911d753de8a7081"
dependencies = [
 "rustls 0.21.12",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e727b36a1a0e8b74c376ac2211e40c2c8af09fb4013c60d910495810f008e9b"
dependencies = [
 "rustls 0.23.26",
 "tokio",
]

[[package]]
name = "tokio-socks"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d4770b8024672c1101b3f6733eab95b18007dbe0847a8afe341fcf79e06043f"
dependencies = [
 "either",
 "futures-io",
 "futures-util",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eca58d7bba4a75707817a2c44174253f9236b2d5fbd055602e9d5c07c139a047"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-tungstenite"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "212d5dcb2a1ce06d81107c3d0ffa3121fe974b73f068c8282cb1c32328113b6c"
dependencies = [
 "futures-util",
 "log",
 "tokio",
 "tungstenite 0.20.1",
]

[[package]]
name = "tokio-tungstenite"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c83b561d025642014097b66e6c1bb422783339e0909e4429cde4749d1990bc38"
dependencies = [
 "futures-util",
 "log",
 "tokio",
 "tungstenite 0.21.0",
]

[[package]]
name = "tokio-tungstenite"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a9daff607c6d2bf6c16fd681ccb7eecc83e4e2cdc1ca067ffaadfca5de7f084"
dependencies = [
 "futures-util",
 "log",
 "rustls 0.23.26",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls 0.26.2",
 "tungstenite 0.26.2",
]

[[package]]
name = "tokio-util"
version = "0.7.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b9590b93e6fcc1739458317cccd391ad3955e2bde8913edf6f95f9e65a8f034"
dependencies = [
 "bytes 1.10.1",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "toml"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f7f0dd8d50a853a531c426359045b1998f04219d88799810762cd4ad314234"
dependencies = [
 "serde",
]

[[package]]
name = "toml"
version = "0.8.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd87a5cdd6ffab733b2f74bc4fd7ee5fff6634124999ac278c35fc78c6120148"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit",
]

[[package]]
name = "toml_datetime"
version = "0.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3da5db5a963e24bc68be8b17b6fa82814bb22ee8660f192bb182771d498f09a3"
dependencies = [
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.22.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "310068873db2c5b3e7659d2cc35d21855dbafa50d1ce336397c666e3cb08137e"
dependencies = [
 "indexmap",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_write",
 "winnow",
]

[[package]]
name = "toml_write"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfb942dfe1d8e29a7ee7fcbde5bd2b9a25fb89aa70caea2eba3bee836ff41076"

[[package]]
name = "toolchain_selector"
version = "0.1.0"
dependencies = [
 "editor",
 "fuzzy",
 "gpui",
 "language",
 "picker",
 "project",
 "ui",
 "util",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "topological-sort"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea68304e134ecd095ac6c3574494fc62b909f416c4fca77e440530221e549d3d"

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "pin-project",
 "pin-project-lite",
 "tokio",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d039ad9159c98b70ecfd540b2573b97f7f52c3e8d9f8ad57a24b916a536975f9"
dependencies = [
 "futures-core",
 "futures-util",
 "pin-project-lite",
 "sync_wrapper 1.0.2",
 "tokio",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-http"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f873044bf02dd1e8239e9c1293ea39dad76dc594ec16185d0a1bf31d8dc8d858"
dependencies = [
 "bitflags 1.3.2",
 "bytes 1.10.1",
 "futures-core",
 "futures-util",
 "http 0.2.12",
 "http-body 0.4.6",
 "http-range-header",
 "pin-project-lite",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-http"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61c5bb1d698276a2443e5ecfabc1008bf15a36c12e6a7176e7bf089ea9131140"
dependencies = [
 "bitflags 2.9.0",
 "bytes 1.10.1",
 "futures-core",
 "futures-util",
 "http 0.2.12",
 "http-body 0.4.6",
 "http-range-header",
 "pin-project-lite",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-layer"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "121c2a6cda46980bb0fcd1647ffaf6cd3fc79a013de288782836f6df9c48780e"

[[package]]
name = "tower-service"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8df9b6e13f2d32c91b9bd719c00d1958837bc7dec474d94952798cc8e69eeec3"

[[package]]
name = "tracing"
version = "0.1.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "784e0ac535deb450455cbfa28a6f0df145ea1bb7ae51b821cf5e7927fdcfbdd0"
dependencies = [
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "395ae124c09f9e6918a2310af6038fba074bcf474ac352496d5910dd59a2226d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tracing-core"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e672c95779cf947c5311f83787af4fa8fffd12fb27e4993211a84bdfd9610f9c"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-log"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee855f1f400bd0e5c02d150ae5de3840039a3f54b025156404e34c23c03f47c3"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-serde"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "704b1aeb7be0d0a84fc9828cae51dab5970fee5088f83d1dd7ee6f6246fc6ff1"
dependencies = [
 "serde",
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.3.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8189decb5ac0fa7bc8b96b7cb9b2701d60d48805aca84a238004d665fcc4008"
dependencies = [
 "matchers",
 "nu-ansi-term 0.46.0",
 "once_cell",
 "regex",
 "serde",
 "serde_json",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "tracing",
 "tracing-core",
 "tracing-log",
 "tracing-serde",
]

[[package]]
name = "trait-variant"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70977707304198400eb4835a78f6a9f928bf41bba420deb8fdb175cd965d77a7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tree-sitter"
version = "0.25.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7cf18d43cbf0bfca51f657132cc616a5097edc4424d538bae6fa60142eaf9f0"
dependencies = [
 "cc",
 "regex",
 "regex-syntax 0.8.5",
 "serde_json",
 "streaming-iterator",
 "tree-sitter-language",
 "wasmtime-c-api-impl",
]

[[package]]
name = "tree-sitter-bash"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "871b0606e667e98a1237ebdc1b0d7056e0aebfdc3141d12b399865d4cb6ed8a6"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-c"
version = "0.23.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afd2b1bf1585dc2ef6d69e87d01db8adb059006649dd5f96f31aa789ee6e9c71"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-cpp"
version = "0.23.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df2196ea9d47b4ab4a31b9297eaa5a5d19a0b121dceb9f118f6790ad0ab94743"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-css"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ad6489794d41350d12a7fbe520e5199f688618f43aace5443980d1ddcf1b29e"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-diff"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfe1e5ca280a65dfe5ba4205c1bcc84edf486464fed315db53dee6da9a335889"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-elixir"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e45d444647b4fd53d8fd32474c1b8bedc1baa22669ce3a78d083e365fa9a2d3f"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-embedded-template"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "790063ef14e5b67556abc0b3be0ed863fb41d65ee791cf8c0b20eb42a1fa46af"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-gitcommit"
version = "0.0.1"
source = "git+https://github.com/zed-industries/tree-sitter-git-commit?rev=88309716a69dd13ab83443721ba6e0b491d37ee9#88309716a69dd13ab83443721ba6e0b491d37ee9"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-go"
version = "0.23.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b13d476345220dbe600147dd444165c5791bf85ef53e28acbedd46112ee18431"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-gomod"
version = "1.1.1"
source = "git+https://github.com/camdencheek/tree-sitter-go-mod?rev=6efb59652d30e0e9cd5f3b3a669afd6f1a926d3c#6efb59652d30e0e9cd5f3b3a669afd6f1a926d3c"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-gowork"
version = "0.0.1"
source = "git+https://github.com/zed-industries/tree-sitter-go-work?rev=acb0617bf7f4fda02c6217676cc64acb89536dc7#acb0617bf7f4fda02c6217676cc64acb89536dc7"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-heex"
version = "0.0.1"
source = "git+https://github.com/zed-industries/tree-sitter-heex?rev=1dd45142fbb05562e35b2040c6129c9bca346592#1dd45142fbb05562e35b2040c6129c9bca346592"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-html"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "261b708e5d92061ede329babaaa427b819329a9d427a1d710abb0f67bbef63ee"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-jsdoc"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3862dfcb1038fc5e7812d7df14190afdeb7e1415288fd5f51f58395f8cb0faf"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-json"
version = "0.24.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d727acca406c0020cffc6cf35516764f36c8e3dc4408e5ebe2cb35a947ec471"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-language"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4013970217383f67b18aef68f6fb2e8d409bc5755227092d32efb0422ba24b8"

[[package]]
name = "tree-sitter-md"
version = "0.3.2"
source = "git+https://github.com/tree-sitter-grammars/tree-sitter-markdown?rev=9a23c1a96c0513d8fc6520972beedd419a973539#9a23c1a96c0513d8fc6520972beedd419a973539"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-python"
version = "0.23.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d065aaa27f3aaceaf60c1f0e0ac09e1cb9eb8ed28e7bcdaa52129cffc7f4b04"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-regex"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712656f8c262a5a4b7d6026e6246950787d178d613864952554e1516a33ab0c1"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-ruby"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be0484ea4ef6bb9c575b4fdabde7e31340a8d2dbc7d52b321ac83da703249f95"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-rust"
version = "0.24.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b9b18034c684a2420722be8b2a91c9c44f2546b631c039edf575ccba8c61be1"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-typescript"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c5f76ed8d947a75cc446d5fccd8b602ebf0cde64ccf2ffa434d873d7a575eff"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "tree-sitter-yaml"
version = "0.6.1"
source = "git+https://github.com/zed-industries/tree-sitter-yaml?rev=baff0b51c64ef6a1fb1f8390f3ad6015b83ec13a#baff0b51c64ef6a1fb1f8390f3ad6015b83ec13a"
dependencies = [
 "cc",
 "tree-sitter-language",
]

[[package]]
name = "try-lock"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e421abadd41a4225275504ea4d6566923418b7f05506fbc9c0fe86ba7396114b"

[[package]]
name = "ttf-parser"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17f77d76d837a7830fe1d4f12b7b4ba4192c1888001c7164257e4bc6d21d96b4"

[[package]]
name = "ttf-parser"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c591d83f69777866b9126b24c6dd9a18351f177e49d625920d19f989fd31cf8"

[[package]]
name = "ttf-parser"
version = "0.25.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2df906b07856748fa3f6e0ad0cbaa047052d4a7dd609e231c4f72cee8c36f31"
dependencies = [
 "core_maths",
]

[[package]]
name = "tungstenite"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e3dac10fd62eaf6617d3a904ae222845979aec67c615d1c842b4002c7666fb9"
dependencies = [
 "byteorder",
 "bytes 1.10.1",
 "data-encoding",
 "http 0.2.12",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha1",
 "thiserror 1.0.69",
 "url",
 "utf-8",
]

[[package]]
name = "tungstenite"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ef1a641ea34f399a848dea702823bbecfb4c486f911735368f1f137cb8257e1"
dependencies = [
 "byteorder",
 "bytes 1.10.1",
 "data-encoding",
 "http 1.3.1",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha1",
 "thiserror 1.0.69",
 "url",
 "utf-8",
]

[[package]]
name = "tungstenite"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4793cb5e56680ecbb1d843515b23b6de9a75eb04b66643e256a396d43be33c13"
dependencies = [
 "bytes 1.10.1",
 "data-encoding",
 "http 1.3.1",
 "httparse",
 "log",
 "rand 0.9.1",
 "rustls 0.23.26",
 "rustls-pki-types",
 "sha1",
 "thiserror 2.0.12",
 "utf-8",
]

[[package]]
name = "typeid"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc7d623258602320d5c55d1bc22793b57daff0ec7efc270ea7d55ce1d5f5471c"

[[package]]
name = "typenum"
version = "1.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1dccffe3ce07af9386bfd29e80c0ab1a8205a2fc34e4bcd40364df902cfa8f3f"

[[package]]
name = "ucd-trie"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2896d95c02a80c6d6a5d6e953d479f5ddf2dfdb6a244441010e373ac0fb88971"

[[package]]
name = "uds_windows"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89daebc3e6fd160ac4aa9fc8b3bf71e1f74fbf92367ae71fb83a037e8bf164b9"
dependencies = [
 "memoffset",
 "tempfile",
 "winapi",
]

[[package]]
name = "ui"
version = "0.1.0"
dependencies = [
 "chrono",
 "component",
 "documented",
 "gpui",
 "gpui_macros",
 "icons",
 "itertools 0.14.0",
 "menu",
 "serde",
 "settings",
 "smallvec",
 "story",
 "strum 0.27.1",
 "theme",
 "ui_macros",
 "util",
 "windows 0.61.1",
 "workspace-hack",
]

[[package]]
name = "ui_input"
version = "0.1.0"
dependencies = [
 "component",
 "editor",
 "gpui",
 "settings",
 "theme",
 "ui",
 "workspace-hack",
]

[[package]]
name = "ui_macros"
version = "0.1.0"
dependencies = [
 "quote",
 "syn 2.0.101",
 "workspace-hack",
]

[[package]]
name = "ui_prompt"
version = "0.1.0"
dependencies = [
 "gpui",
 "markdown",
 "menu",
 "settings",
 "theme",
 "ui",
 "workspace",
 "workspace-hack",
]

[[package]]
name = "uluru"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c8a2469e56e6e5095c82ccd3afb98dad95f7af7929aab6d8ba8d6e0f73657da"
dependencies = [
 "arrayvec",
]

[[package]]
name = "unicase"
version = "2.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b844d17643ee918803943289730bec8aac480150456169e647ed0b576ba539"

[[package]]
name = "unicode-bidi"
version = "0.3.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c1cb5db39152898a79168971543b1cb5020dff7fe43c8dc468b0885f5e29df5"

[[package]]
name = "unicode-bidi-mirroring"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23cb788ffebc92c5948d0e997106233eeb1d8b9512f93f41651f52b6c5f5af86"

[[package]]
name = "unicode-bidi-mirroring"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5dfa6e8c60bb66d49db113e0125ee8711b7647b5579dc7f5f19c42357ed039fe"

[[package]]
name = "unicode-bom"
version = "2.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7eec5d1121208364f6793f7d2e222bf75a915c19557537745b195b253dd64217"

[[package]]
name = "unicode-ccc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1df77b101bcc4ea3d78dafc5ad7e4f58ceffe0b2b16bf446aeb50b6cb4157656"

[[package]]
name = "unicode-ccc"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce61d488bcdc9bc8b5d1772c404828b17fc481c0a582b5581e95fb233aef503e"

[[package]]
name = "unicode-ident"
version = "1.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a5f39404a5da50712a4c1eecf25e90dd62b613502b7e925fd4e4d19b5c96512"

[[package]]
name = "unicode-linebreak"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b09c83c3c29d37506a3e260c08c03743a6bb66a9cd432c6934ab501a190571f"

[[package]]
name = "unicode-normalization"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5033c97c4262335cded6d6fc3e5c18ab755e1a3dc96376350f3d8e9f009ad956"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-properties"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e70f2a8b45122e719eb623c01822704c4e0907e7e426a05927e1a1cfff5b75d0"

[[package]]
name = "unicode-script"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fb421b350c9aff471779e262955939f565ec18b86c15364e6bdf0d662ca7c1f"

[[package]]
name = "unicode-segmentation"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6ccf251212114b54433ec949fd6a7841275f9ada20dddd2f29e9ceea4501493"

[[package]]
name = "unicode-vo"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1d386ff53b415b7fe27b50bb44679e2cc4660272694b7b6f3326d8480823a94"

[[package]]
name = "unicode-width"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dd6e30e90baa6f72411720665d41d89b9a3d039dc45b8faea1ddd07f617f6af"

[[package]]
name = "unicode-width"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fc81956842c57dac11422a97c3b8195a1ff727f06e85c84ed2e8aa277c9a0fd"

[[package]]
name = "unicode-xid"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc1c04c71510c7f702b52b7c350734c9ff1295c464a03335b00bb84fc54f853"

[[package]]
name = "unicode_categories"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39ec24b3121d976906ece63c9daad25b85969647682eee313cb5779fdd69e14e"

[[package]]
name = "unindent"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7264e107f553ccae879d21fbea1d6724ac785e8c3bfc762137959b5802826ef3"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna",
 "percent-encoding",
 "serde",
]

[[package]]
name = "urlencoding"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daf8dba3b7eb870caf1ddeed7bc9d2a049f3cfdfae7cb521b087cc33ae4c49da"

[[package]]
name = "usvg"
version = "0.45.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80be9b06fbae3b8b303400ab20778c80bbaf338f563afe567cf3c9eea17b47ef"
dependencies = [
 "base64 0.22.1",
 "data-url",
 "flate2",
 "fontdb 0.23.0",
 "imagesize",
 "kurbo",
 "log",
 "pico-args",
 "roxmltree",
 "rustybuzz 0.20.1",
 "simplecss",
 "siphasher",
 "strict-num",
 "svgtypes",
 "tiny-skia-path",
 "unicode-bidi",
 "unicode-script",
 "unicode-vo",
 "xmlwriter",
]

[[package]]
name = "utf-8"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09cc8ee72d2a9becf2f2febe0205bbed8fc6615b7cb429ad062dc7b7ddd036a9"

[[package]]
name = "utf16_iter"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8232dd3cdaed5356e0f716d285e4b40b932ac434100fe9b7e0e8e935b9e6246"

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "utf8parse"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06abde3611657adf66d383f00b093d7faecc7fa57071cce2578660c9f1010821"

[[package]]
name = "util"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-fs",
 "async_zip",
 "collections",
 "command-fds",
 "dirs 4.0.0",
 "dunce",
 "futures 0.3.31",
 "futures-lite 1.13.0",
 "git2",
 "globset",
 "indoc",
 "itertools 0.14.0",
 "libc",
 "log",
 "rand 0.8.5",
 "regex",
 "rust-embed",
 "serde",
 "serde_json",
 "serde_json_lenient",
 "smol",
 "take-until",
 "tempfile",
 "tendril",
 "unicase",
 "util_macros",
 "walkdir",
 "workspace-hack",
]

[[package]]
name = "util_macros"
version = "0.1.0"
dependencies = [
 "quote",
 "syn 2.0.101",
 "workspace-hack",
]

[[package]]
name = "uuid"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "458f7a779bf54acc9f347480ac654f68407d3aab21269a6e3c9f922acd9e2da9"
dependencies = [
 "getrandom 0.3.2",
 "serde",
 "sha1_smol",
]

[[package]]
name = "uuid-simd"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23b082222b4f6619906941c17eb2297fff4c2fb96cb60164170522942a200bd8"
dependencies = [
 "outref",
 "uuid",
 "vsimd",
]

[[package]]
name = "v_frame"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6f32aaa24bacd11e488aa9ba66369c7cd514885742c9fe08cfe85884db3e92b"
dependencies = [
 "aligned-vec",
 "num-traits",
 "wasm-bindgen",
]

[[package]]
name = "valuable"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba73ea9cf16a25df0c8caa16c51acb937d5712a8429db78a3ee29d5dcacd3a65"

[[package]]
name = "value-bag"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "943ce29a8a743eb10d6082545d861b24f9d1b160b7d741e0f2cdf726bec909c5"
dependencies = [
 "value-bag-serde1",
 "value-bag-sval2",
]

[[package]]
name = "value-bag-serde1"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35540706617d373b118d550d41f5dfe0b78a0c195dc13c6815e92e2638432306"
dependencies = [
 "erased-serde",
 "serde",
 "serde_fmt",
]

[[package]]
name = "value-bag-sval2"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fe7e140a2658cc16f7ee7a86e413e803fc8f9b5127adc8755c19f9fefa63a52"
dependencies = [
 "sval",
 "sval_buffer",
 "sval_dynamic",
 "sval_fmt",
 "sval_json",
 "sval_ref",
 "sval_serde",
]

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "version-compare"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "852e951cb7832cb45cb1169900d19760cfa39b82bc0ea9c0e5a14ae88411c98b"

[[package]]
name = "version_check"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b928f33d975fc6ad9f86c8f283853ad26bdd5b10b7f1542aa2fa15e2289105a"

[[package]]
name = "vim"
version = "0.1.0"
dependencies = [
 "anyhow",
 "assets",
 "async-compat",
 "async-trait",
 "collections",
 "command_palette",
 "command_palette_hooks",
 "db",
 "editor",
 "futures 0.3.31",
 "git_ui",
 "gpui",
 "indoc",
 "itertools 0.14.0",
 "language",
 "log",
 "lsp",
 "multi_buffer",
 "nvim-rs",
 "parking_lot",
 "picker",
 "project",
 "project_panel",
 "regex",
 "release_channel",
 "schemars",
 "search",
 "serde",
 "serde_derive",
 "serde_json",
 "settings",
 "task",
 "text",
 "theme",
 "tokio",
 "ui",
 "util",
 "vim_mode_setting",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "vim_mode_setting"
version = "0.1.0"
dependencies = [
 "anyhow",
 "gpui",
 "settings",
 "workspace-hack",
]

[[package]]
name = "vscode_theme"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b3666211944f2e6ba2c359bc9efc1891157e910b1b11c3900892ea9f18179d2"
dependencies = [
 "serde",
]

[[package]]
name = "vsimd"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c3082ca00d5a5ef149bb8b555a72ae84c9c59f7250f013ac822ac2e49b19c64"

[[package]]
name = "vswhom"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be979b7f07507105799e854203b470ff7c78a1639e330a58f183b5fea574608b"
dependencies = [
 "libc",
 "vswhom-sys",
]

[[package]]
name = "vswhom-sys"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb067e4cbd1ff067d1df46c9194b5de0e98efd2810bbc95c5d5e5f25a3231150"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "vte"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5924018406ce0063cd67f8e008104968b74b563ee1b85dde3ed1f7cb87d3dbd"
dependencies = [
 "arrayvec",
 "bitflags 2.9.0",
 "cursor-icon",
 "log",
 "memchr",
 "serde",
]

[[package]]
name = "waker-fn"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "317211a0dc0ceedd78fb2ca9a44aed3d7b9b26f81870d485c07122b4350673b7"

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa7760aed19e106de2c7c0b581b509f2f25d3dacaf737cb82ac61bc6d760b0e"
dependencies = [
 "try-lock",
]

[[package]]
name = "warp"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4378d202ff965b011c64817db11d5829506d3404edeadb61f190d111da3f231c"
dependencies = [
 "bytes 1.10.1",
 "futures-channel",
 "futures-util",
 "headers",
 "http 0.2.12",
 "hyper 0.14.32",
 "log",
 "mime",
 "mime_guess",
 "percent-encoding",
 "pin-project",
 "scoped-tls",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "tokio",
 "tokio-tungstenite 0.21.0",
 "tokio-util",
 "tower-service",
 "tracing",
]

[[package]]
name = "wasi"
version = "0.9.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cccddf32554fecc6acb585f82a32a72e28b48f8c4c1883ddfeeeaa96f7d8e519"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasi"
version = "0.14.2+wasi-0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9683f9a5a998d873c0d21fcbe3c083009670149a8fab228644b8bd36b2c48cb3"
dependencies = [
 "wit-bindgen-rt 0.39.0",
]

[[package]]
name = "wasite"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8dad83b4f25e74f184f64c43b150b91efe7647395b42289f38e50566d82855b"

[[package]]
name = "wasm-bindgen"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1edc8929d7499fc4e8f0be2262a241556cfc54a0bea223790e71446f2aab1ef5"
dependencies = [
 "cfg-if",
 "once_cell",
 "rustversion",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f0a0651a5c2bc21487bde11ee802ccaf4c51935d0d3d42a6101f98161700bc6"
dependencies = [
 "bumpalo",
 "log",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "555d470ec0bc3bb57890405e5d4322cc9ea83cebb085523ced7be4144dac1e61"
dependencies = [
 "cfg-if",
 "js-sys",
 "once_cell",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fe63fc6d09ed3792bd0897b314f53de8e16568c2b3f7982f468c0bf9bd0b407"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ae87ea40c9f689fc23f209965b6fb8a99ad69aeeb0231408be24920604395de"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a05d73b933a847d6cccdda8f838a22ff101ad9bf93e33684f39c1f5f0eece3d"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "wasm-encoder"
version = "0.201.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9c7d2731df60006819b013f64ccc2019691deccf6e11a1804bc850cd6748f1a"
dependencies = [
 "leb128",
]

[[package]]
name = "wasm-encoder"
version = "0.221.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc8444fe4920de80a4fe5ab564fff2ae58b6b73166b89751f8c6c93509da32e5"
dependencies = [
 "leb128",
 "wasmparser 0.221.3",
]

[[package]]
name = "wasm-encoder"
version = "0.227.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80bb72f02e7fbf07183443b27b0f3d4144abf8c114189f2e088ed95b696a7822"
dependencies = [
 "leb128fmt",
 "wasmparser 0.227.1",
]

[[package]]
name = "wasm-metadata"
version = "0.201.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fd83062c17b9f4985d438603cde0a5e8c5c8198201a6937f778b607924c7da2"
dependencies = [
 "anyhow",
 "indexmap",
 "serde",
 "serde_derive",
 "serde_json",
 "spdx",
 "wasm-encoder 0.201.0",
 "wasmparser 0.201.0",
]

[[package]]
name = "wasm-metadata"
version = "0.227.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce1ef0faabbbba6674e97a56bee857ccddf942785a336c8b47b42373c922a91d"
dependencies = [
 "anyhow",
 "auditable-serde",
 "flate2",
 "indexmap",
 "serde",
 "serde_derive",
 "serde_json",
 "spdx",
 "url",
 "wasm-encoder 0.227.1",
 "wasmparser 0.227.1",
]

[[package]]
name = "wasm-streams"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15053d8d85c7eccdbefef60f06769760a563c7f0a9d6902a13d35c7800b0ad65"
dependencies = [
 "futures-util",
 "js-sys",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "wasmparser"
version = "0.201.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84e5df6dba6c0d7fafc63a450f1738451ed7a0b52295d83e868218fa286bf708"
dependencies = [
 "bitflags 2.9.0",
 "indexmap",
 "semver",
]

[[package]]
name = "wasmparser"
version = "0.221.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d06bfa36ab3ac2be0dee563380147a5b81ba10dd8885d7fbbc9eb574be67d185"
dependencies = [
 "bitflags 2.9.0",
 "hashbrown 0.15.3",
 "indexmap",
 "semver",
 "serde",
]

[[package]]
name = "wasmparser"
version = "0.227.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f51cad774fb3c9461ab9bccc9c62dfb7388397b5deda31bf40e8108ccd678b2"
dependencies = [
 "bitflags 2.9.0",
 "hashbrown 0.15.3",
 "indexmap",
 "semver",
]

[[package]]
name = "wasmprinter"
version = "0.221.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7343c42a97f2926c7819ff81b64012092ae954c5d83ddd30c9fcdefd97d0b283"
dependencies = [
 "anyhow",
 "termcolor",
 "wasmparser 0.221.3",
]

[[package]]
name = "wasmtime"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11976a250672556d1c4c04c6d5d7656ac9192ac9edc42a4587d6c21460010e69"
dependencies = [
 "anyhow",
 "async-trait",
 "bitflags 2.9.0",
 "bumpalo",
 "cc",
 "cfg-if",
 "encoding_rs",
 "hashbrown 0.14.5",
 "indexmap",
 "libc",
 "log",
 "mach2",
 "memfd",
 "object",
 "once_cell",
 "paste",
 "postcard",
 "psm",
 "pulley-interpreter",
 "rayon",
 "rustix 0.38.44",
 "semver",
 "serde",
 "serde_derive",
 "smallvec",
 "sptr",
 "target-lexicon 0.13.2",
 "trait-variant",
 "wasmparser 0.221.3",
 "wasmtime-asm-macros",
 "wasmtime-component-macro",
 "wasmtime-component-util",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "wasmtime-fiber",
 "wasmtime-jit-icache-coherence",
 "wasmtime-math",
 "wasmtime-slab",
 "wasmtime-versioned-export-macros",
 "wasmtime-winch",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasmtime-asm-macros"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f178b0d125201fbe9f75beaf849bd3e511891f9e45ba216a5b620802ccf64f2"
dependencies = [
 "cfg-if",
]

[[package]]
name = "wasmtime-c-api-impl"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea30cef3608f2de5797c7bbb94c1ba4f3676d9a7f81ae86ced1b512e2766ed0c"
dependencies = [
 "anyhow",
 "log",
 "tracing",
 "wasmtime",
 "wasmtime-c-api-macros",
]

[[package]]
name = "wasmtime-c-api-macros"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "022a79ebe1124d5d384d82463d7e61c6b4dd857d81f15cb8078974eeb86db65b"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "wasmtime-component-macro"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d74de6592ed945d0a602f71243982a304d5d02f1e501b638addf57f42d57dfaf"
dependencies = [
 "anyhow",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasmtime-component-util",
 "wasmtime-wit-bindgen",
 "wit-parser 0.221.3",
]

[[package]]
name = "wasmtime-component-util"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "707dc7b3c112ab5a366b30cfe2fb5b2f8e6a0f682f16df96a5ec582bfe6f056e"

[[package]]
name = "wasmtime-cranelift"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "366be722674d4bf153290fbcbc4d7d16895cc82fb3e869f8d550ff768f9e9e87"
dependencies = [
 "anyhow",
 "cfg-if",
 "cranelift-codegen",
 "cranelift-control",
 "cranelift-entity",
 "cranelift-frontend",
 "cranelift-native",
 "gimli",
 "itertools 0.12.1",
 "log",
 "object",
 "smallvec",
 "target-lexicon 0.13.2",
 "thiserror 1.0.69",
 "wasmparser 0.221.3",
 "wasmtime-environ",
 "wasmtime-versioned-export-macros",
]

[[package]]
name = "wasmtime-environ"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cdadc1af7097347aa276a4f008929810f726b5b46946971c660b6d421e9994ad"
dependencies = [
 "anyhow",
 "cpp_demangle",
 "cranelift-bitset",
 "cranelift-entity",
 "gimli",
 "indexmap",
 "log",
 "object",
 "postcard",
 "rustc-demangle",
 "semver",
 "serde",
 "serde_derive",
 "smallvec",
 "target-lexicon 0.13.2",
 "wasm-encoder 0.221.3",
 "wasmparser 0.221.3",
 "wasmprinter",
 "wasmtime-component-util",
]

[[package]]
name = "wasmtime-fiber"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccba90d4119f081bca91190485650730a617be1fff5228f8c4757ce133d21117"
dependencies = [
 "anyhow",
 "cc",
 "cfg-if",
 "rustix 0.38.44",
 "wasmtime-asm-macros",
 "wasmtime-versioned-export-macros",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasmtime-jit-icache-coherence"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec5e8552e01692e6c2e5293171704fed8abdec79d1a6995a0870ab190e5747d1"
dependencies = [
 "anyhow",
 "cfg-if",
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasmtime-math"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29210ec2aa25e00f4d54605cedaf080f39ec01a872c5bd520ad04c67af1dde17"
dependencies = [
 "libm",
]

[[package]]
name = "wasmtime-slab"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcb5821a96fa04ac14bc7b158bb3d5cd7729a053db5a74dad396cd513a5e5ccf"

[[package]]
name = "wasmtime-versioned-export-macros"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86ff86db216dc0240462de40c8290887a613dddf9685508eb39479037ba97b5b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "wasmtime-wasi"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d1be69bfcab1bdac74daa7a1f9695ab992b9c8e21b9b061e7d66434097e0ca4"
dependencies = [
 "anyhow",
 "async-trait",
 "bitflags 2.9.0",
 "bytes 1.10.1",
 "cap-fs-ext",
 "cap-net-ext",
 "cap-rand",
 "cap-std",
 "cap-time-ext",
 "fs-set-times",
 "futures 0.3.31",
 "io-extras",
 "io-lifetimes",
 "rustix 0.38.44",
 "system-interface",
 "thiserror 1.0.69",
 "tokio",
 "tracing",
 "trait-variant",
 "url",
 "wasmtime",
 "wiggle",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasmtime-winch"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdbabfb8f20502d5e1d81092b9ead3682ae59988487aafcd7567387b7a43cf8f"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "gimli",
 "object",
 "target-lexicon 0.13.2",
 "wasmparser 0.221.3",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "winch-codegen",
]

[[package]]
name = "wasmtime-wit-bindgen"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8358319c2dd1e4db79e3c1c5d3a5af84956615343f9f89f4e4996a36816e06e6"
dependencies = [
 "anyhow",
 "heck 0.5.0",
 "indexmap",
 "wit-parser 0.221.3",
]

[[package]]
name = "wast"
version = "35.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ef140f1b49946586078353a453a1d28ba90adfc54dde75710bc1931de204d68"
dependencies = [
 "leb128",
]

[[package]]
name = "watch"
version = "0.1.0"
dependencies = [
 "ctor",
 "futures 0.3.31",
 "gpui",
 "parking_lot",
 "rand 0.8.5",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "wayland-backend"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7208998eaa3870dad37ec8836979581506e0c5c64c20c9e79e9d2a10d6f47bf"
dependencies = [
 "cc",
 "downcast-rs",
 "rustix 0.38.44",
 "scoped-tls",
 "smallvec",
 "wayland-sys",
]

[[package]]
name = "wayland-client"
version = "0.31.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2120de3d33638aaef5b9f4472bff75f07c56379cf76ea320bd3a3d65ecaf73f"
dependencies = [
 "bitflags 2.9.0",
 "rustix 0.38.44",
 "wayland-backend",
 "wayland-scanner",
]

[[package]]
name = "wayland-cursor"
version = "0.31.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a93029cbb6650748881a00e4922b076092a6a08c11e7fbdb923f064b23968c5d"
dependencies = [
 "rustix 0.38.44",
 "wayland-client",
 "xcursor",
]

[[package]]
name = "wayland-protocols"
version = "0.31.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f81f365b8b4a97f422ac0e8737c438024b5951734506b0e1d775c73030561f4"
dependencies = [
 "bitflags 2.9.0",
 "wayland-backend",
 "wayland-client",
 "wayland-scanner",
]

[[package]]
name = "wayland-protocols-plasma"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23803551115ff9ea9bce586860c5c5a971e360825a0309264102a9495a5ff479"
dependencies = [
 "bitflags 2.9.0",
 "wayland-backend",
 "wayland-client",
 "wayland-protocols",
 "wayland-scanner",
]

[[package]]
name = "wayland-scanner"
version = "0.31.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "896fdafd5d28145fce7958917d69f2fd44469b1d4e861cb5961bcbeebc6d1484"
dependencies = [
 "proc-macro2",
 "quick-xml 0.37.4",
 "quote",
]

[[package]]
name = "wayland-sys"
version = "0.31.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbcebb399c77d5aa9fa5db874806ee7b4eba4e73650948e8f93963f128896615"
dependencies = [
 "dlib",
 "log",
 "once_cell",
 "pkg-config",
]

[[package]]
name = "web-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33b6dd2ef9186f1f2072e409e99cd22a975331a6b3591b12c764e0e55c60d5d2"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "web-time"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a6580f308b1fad9207618087a65c04e7a10bc77e02c8e84e9b00dd4b12fa0bb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "web_atoms"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "954c5a41f2bcb7314344079d0891505458cc2f4b422bdea1d5bfbe6d1a04903b"
dependencies = [
 "phf",
 "phf_codegen",
 "string_cache",
 "string_cache_codegen",
]

[[package]]
name = "web_search"
version = "0.1.0"
dependencies = [
 "anyhow",
 "collections",
 "gpui",
 "serde",
 "workspace-hack",
 "zed_llm_client",
]

[[package]]
name = "web_search_providers"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "language_model",
 "serde",
 "serde_json",
 "web_search",
 "workspace-hack",
 "zed_llm_client",
]

[[package]]
name = "webpki-root-certs"
version = "0.26.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09aed61f5e8d2c18344b3faa33a4c837855fe56642757754775548fee21386c4"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "webpki-roots"
version = "0.26.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2210b291f7ea53617fbafcc4939f10914214ec15aace5ba62293a668f322c5c9"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "webrtc-sys"
version = "0.3.7"
source = "git+https://github.com/zed-industries/livekit-rust-sdks?rev=80bb8f4c9112789f7c24cc98d8423010977806a6#80bb8f4c9112789f7c24cc98d8423010977806a6"
dependencies = [
 "cc",
 "cxx",
 "cxx-build",
 "glob",
 "log",
 "webrtc-sys-build",
]

[[package]]
name = "webrtc-sys-build"
version = "0.3.6"
source = "git+https://github.com/zed-industries/livekit-rust-sdks?rev=80bb8f4c9112789f7c24cc98d8423010977806a6#80bb8f4c9112789f7c24cc98d8423010977806a6"
dependencies = [
 "fs2",
 "regex",
 "reqwest 0.11.27",
 "scratch",
 "semver",
 "zip",
]

[[package]]
name = "weezl"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53a85b86a771b1c87058196170769dd264f66c0782acf1ae6cc51bfd64b39082"

[[package]]
name = "welcome"
version = "0.1.0"
dependencies = [
 "anyhow",
 "client",
 "component",
 "db",
 "documented",
 "editor",
 "fuzzy",
 "gpui",
 "install_cli",
 "language",
 "picker",
 "project",
 "schemars",
 "serde",
 "settings",
 "telemetry",
 "theme",
 "ui",
 "util",
 "vim_mode_setting",
 "workspace",
 "workspace-hack",
 "zed_actions",
]

[[package]]
name = "which"
version = "4.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87ba24419a2078cd2b0f2ede2691b6c66d8e47836da3b6db8265ebad47afbfc7"
dependencies = [
 "either",
 "home",
 "once_cell",
 "rustix 0.38.44",
]

[[package]]
name = "which"
version = "6.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4ee928febd44d98f2f459a4a79bd4d928591333a494a10a868418ac1b39cf1f"
dependencies = [
 "either",
 "home",
 "rustix 0.38.44",
 "winsafe",
]

[[package]]
name = "whoami"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6994d13118ab492c3c80c1f81928718159254c53c472bf9ce36f8dae4add02a7"
dependencies = [
 "redox_syscall 0.5.11",
 "wasite",
]

[[package]]
name = "wiggle"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b9af35bc9629c52c261465320a9a07959164928b4241980ba1cf923b9e6751d"
dependencies = [
 "anyhow",
 "async-trait",
 "bitflags 2.9.0",
 "thiserror 1.0.69",
 "tracing",
 "wasmtime",
 "wiggle-macro",
]

[[package]]
name = "wiggle-generate"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2cf267dd05673912c8138f4b54acabe6bd53407d9d1536f0fadb6520dd16e101"
dependencies = [
 "anyhow",
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "shellexpand 2.1.2",
 "syn 2.0.101",
 "witx",
]

[[package]]
name = "wiggle-macro"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08c5c473d4198e6c2d377f3809f713ff0c110cab88a0805ae099a82119ee250c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wiggle-generate",
]

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf221c93e13a30d793f7645a0e7762c55d169dbb0a49671918a2319d289b10bb"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "winch-codegen"
version = "29.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f849ef2c5f46cb0a20af4b4487aaa239846e52e2c03f13fa3c784684552859c"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "gimli",
 "regalloc2",
 "smallvec",
 "target-lexicon 0.13.2",
 "thiserror 1.0.69",
 "wasmparser 0.221.3",
 "wasmtime-cranelift",
 "wasmtime-environ",
]

[[package]]
name = "windows"
version = "0.54.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9252e5725dbed82865af151df558e754e4a3c2c30818359eb17465f1346a1b49"
dependencies = [
 "windows-core 0.54.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12342cb4d8e3b046f3d80effd474a7a02447231330ef77d71daa6fbc40681143"
dependencies = [
 "windows-core 0.57.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd04d41d93c4992d421894c18c8b43496aa748dd4c081bac0dc93eb0489272b6"
dependencies = [
 "windows-core 0.58.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows"
version = "0.61.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5ee8f3d025738cb02bad7868bbb5f8a6327501e870bf51f1b455b0a2454a419"
dependencies = [
 "windows-collections",
 "windows-core 0.61.0",
 "windows-future",
 "windows-link",
 "windows-numerics",
]

[[package]]
name = "windows-capture"
version = "1.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59d10b4be8b907c7055bc7270dd68d2b920978ffacc1599dcb563a79f0e68d16"
dependencies = [
 "clap",
 "ctrlc",
 "parking_lot",
 "rayon",
 "thiserror 2.0.12",
 "windows 0.61.1",
 "windows-future",
]

[[package]]
name = "windows-collections"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3beeceb5e5cfd9eb1d76b381630e82c4241ccd0d27f1a39ed41b2760b255c5e8"
dependencies = [
 "windows-core 0.61.0",
]

[[package]]
name = "windows-core"
version = "0.54.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12661b9c89351d684a50a8a643ce5f608e20243b9fb84687800163429f161d65"
dependencies = [
 "windows-result 0.1.2",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2ed2439a290666cd67ecce2b0ffaad89c2a56b976b736e6ece670297897832d"
dependencies = [
 "windows-implement 0.57.0",
 "windows-interface 0.57.0",
 "windows-result 0.1.2",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ba6d44ec8c2591c134257ce647b7ea6b20335bf6379a27dac5f1641fcf59f99"
dependencies = [
 "windows-implement 0.58.0",
 "windows-interface 0.58.0",
 "windows-result 0.2.0",
 "windows-strings 0.1.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.61.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4763c1de310c86d75a878046489e2e5ba02c649d185f21c67d4cf8a56d098980"
dependencies = [
 "windows-implement 0.60.0",
 "windows-interface 0.59.1",
 "windows-link",
 "windows-result 0.3.2",
 "windows-strings 0.4.0",
]

[[package]]
name = "windows-future"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a1d6bbefcb7b60acd19828e1bc965da6fcf18a7e39490c5f8be71e54a19ba32"
dependencies = [
 "windows-core 0.61.0",
 "windows-link",
]

[[package]]
name = "windows-implement"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9107ddc059d5b6fbfbffdfa7a7fe3e22a226def0b2608f72e9d552763d3e1ad7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-implement"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bbd5b46c938e506ecbce286b6628a02171d56153ba733b6c741fc627ec9579b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-implement"
version = "0.60.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a47fddd13af08290e67f4acabf4b459f647552718f683a7b415d290ac744a836"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-interface"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29bee4b38ea3cde66011baa44dba677c432a78593e202392d1e9070cf2a7fca7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-interface"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "053c4c462dc91d3b1504c6fe5a726dd15e216ba718e84a0e46a88fbe5ded3515"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-interface"
version = "0.59.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd9211b69f8dcdfa817bfd14bf1c97c9188afa36f4750130fcdf3f400eca9fa8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-link"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76840935b766e1b0a05c0066835fb9ec80071d4c09a16f6bd5f7e655e3c14c38"

[[package]]
name = "windows-numerics"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9150af68066c4c5c07ddc0ce30421554771e528bde427614c61038bc2c92c2b1"
dependencies = [
 "windows-core 0.61.0",
 "windows-link",
]

[[package]]
name = "windows-registry"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4286ad90ddb45071efd1a66dfa43eb02dd0dfbae1545ad6cc3c51cf34d7e8ba3"
dependencies = [
 "windows-result 0.3.2",
 "windows-strings 0.3.1",
 "windows-targets 0.53.0",
]

[[package]]
name = "windows-registry"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad1da3e436dc7653dfdf3da67332e22bff09bb0e28b0239e1624499c7830842e"
dependencies = [
 "windows-link",
 "windows-result 0.3.2",
 "windows-strings 0.4.0",
]

[[package]]
name = "windows-result"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e383302e8ec8515204254685643de10811af0ed97ea37210dc26fb0032647f8"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-result"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d1043d8214f791817bab27572aaa8af63732e11bf84aa21a45a78d6c317ae0e"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-result"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c64fd11a4fd95df68efcfee5f44a294fe71b8bc6a91993e2791938abcc712252"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-strings"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd9b125c486025df0eabcb585e62173c6c9eddcec5d117d3b6e8c30e2ee4d10"
dependencies = [
 "windows-result 0.2.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-strings"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87fa48cc5d406560701792be122a10132491cff9d0aeb23583cc2dcafc847319"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-strings"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a2ba9642430ee452d5a7aa78d72907ebe8cfda358e8cb7918a2050581322f97"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-sys"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75283be5efb2831d37ea142365f009c02ec203cd29a3ebecbc093d52315b66d0"
dependencies = [
 "windows-targets 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm 0.52.6",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1e4c7e8ceaaf9cb7d7507c974735728ab453b67ef8f18febdd7c11fe59dca8b"
dependencies = [
 "windows_aarch64_gnullvm 0.53.0",
 "windows_aarch64_msvc 0.53.0",
 "windows_i686_gnu 0.53.0",
 "windows_i686_gnullvm 0.53.0",
 "windows_i686_msvc 0.53.0",
 "windows_x86_64_gnu 0.53.0",
 "windows_x86_64_gnullvm 0.53.0",
 "windows_x86_64_msvc 0.53.0",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b8d5f90ddd19cb4a147a5fa63ca848db3df085e25fee3cc10b39b6eebae764"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_aarch64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7651a1f62a11b8cbd5e0d42526e55f2c99886c77e007179efff86c2b137e66c"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1dc67659d35f387f5f6c479dc4e28f1d4bb90ddd1a5d3da2e5d97b42d6272c3"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce6ccbdedbf6d6354471319e781c0dfef054c81fbc7cf83f338a4296c0cae11"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_i686_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "581fee95406bb13382d2f65cd4a908ca7b1e4c2f1917f143ba16efe98a589b5d"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e55b5ac9ea33f2fc1716d1742db15574fd6fc8dadc51caab1c16a3d3b4190ba"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a6e035dd0599267ce1ee132e51c27dd29437f63325753051e71dd9e42406c57"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "windows_x86_64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271414315aff87387382ec3d271b52d7ae78726f5d44ac98b4f4030c91880486"

[[package]]
name = "winnow"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63d3fcd9bba44b03821e7d699eeee959f3126dcc4aa8e4ae18ec617c2a5cea10"
dependencies = [
 "memchr",
]

[[package]]
name = "winreg"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80d0f4e272c85def139476380b12f9ac60926689dd2e01d4923222f40580869d"
dependencies = [
 "winapi",
]

[[package]]
name = "winreg"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524e57b2c537c0f9b1e69f1965311ec12182b4122e45035b1508cd24d2adadb1"
dependencies = [
 "cfg-if",
 "windows-sys 0.48.0",
]

[[package]]
name = "winreg"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a277a57398d4bfa075df44f501a17cfdf8542d224f0d36095a2adc7aee4ef0a5"
dependencies = [
 "cfg-if",
 "windows-sys 0.48.0",
]

[[package]]
name = "winreg"
version = "0.55.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb5a765337c50e9ec252c2069be9bf91c7df47afb103b642ba3a53bf8101be97"
dependencies = [
 "cfg-if",
 "windows-sys 0.59.0",
]

[[package]]
name = "winresource"
version = "0.1.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba4a67c78ee5782c0c1cb41bebc7e12c6e79644daa1650ebbc1de5d5b08593f7"
dependencies = [
 "toml 0.8.20",
 "version_check",
]

[[package]]
name = "winsafe"
version = "0.0.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d135d17ab770252ad95e9a872d365cf3090e3be864a34ab46f48555993efc904"

[[package]]
name = "winx"
version = "0.36.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f3fd376f71958b862e7afb20cfe5a22830e1963462f3a17f49d82a6c1d1f42d"
dependencies = [
 "bitflags 2.9.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "wio"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d129932f4644ac2396cb456385cbf9e63b5b30c6e8dc4820bdca4eb082037a5"
dependencies = [
 "winapi",
]

[[package]]
name = "wit-bindgen"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "288f992ea30e6b5c531b52cdd5f3be81c148554b09ea416f058d16556ba92c27"
dependencies = [
 "bitflags 2.9.0",
 "wit-bindgen-rt 0.22.0",
 "wit-bindgen-rust-macro 0.22.0",
]

[[package]]
name = "wit-bindgen"
version = "0.41.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10fb6648689b3929d56bbc7eb1acf70c9a42a29eb5358c67c10f54dbd5d695de"
dependencies = [
 "wit-bindgen-rt 0.41.0",
 "wit-bindgen-rust-macro 0.41.0",
]

[[package]]
name = "wit-bindgen-core"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e85e72719ffbccf279359ad071497e47eb0675fe22106dea4ed2d8a7fcb60ba4"
dependencies = [
 "anyhow",
 "wit-parser 0.201.0",
]

[[package]]
name = "wit-bindgen-core"
version = "0.41.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92fa781d4f2ff6d3f27f3cc9b74a73327b31ca0dc4a3ef25a0ce2983e0e5af9b"
dependencies = [
 "anyhow",
 "heck 0.5.0",
 "wit-parser 0.227.1",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcb8738270f32a2d6739973cbbb7c1b6dd8959ce515578a6e19165853272ee64"

[[package]]
name = "wit-bindgen-rt"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42320e61fe2cfd34354ecb597f86f413484a798ba44a8ca1165c58d42da6c1"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.41.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4db52a11d4dfb0a59f194c064055794ee6564eb1ced88c25da2cf76e50c5621"
dependencies = [
 "bitflags 2.9.0",
 "futures 0.3.31",
 "once_cell",
]

[[package]]
name = "wit-bindgen-rust"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8a39a15d1ae2077688213611209849cad40e9e5cccf6e61951a425850677ff3"
dependencies = [
 "anyhow",
 "heck 0.4.1",
 "indexmap",
 "wasm-metadata 0.201.0",
 "wit-bindgen-core 0.22.0",
 "wit-component 0.201.0",
]

[[package]]
name = "wit-bindgen-rust"
version = "0.41.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d0809dc5ba19e2e98661bf32fc0addc5a3ca5bf3a6a7083aa6ba484085ff3ce"
dependencies = [
 "anyhow",
 "heck 0.5.0",
 "indexmap",
 "prettyplease",
 "syn 2.0.101",
 "wasm-metadata 0.227.1",
 "wit-bindgen-core 0.41.0",
 "wit-component 0.227.1",
]

[[package]]
name = "wit-bindgen-rust-macro"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d376d3ae5850526dfd00d937faea0d81a06fa18f7ac1e26f386d760f241a8f4b"
dependencies = [
 "anyhow",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wit-bindgen-core 0.22.0",
 "wit-bindgen-rust 0.22.0",
]

[[package]]
name = "wit-bindgen-rust-macro"
version = "0.41.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad19eec017904e04c60719592a803ee5da76cb51c81e3f6fbf9457f59db49799"
dependencies = [
 "anyhow",
 "prettyplease",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wit-bindgen-core 0.41.0",
 "wit-bindgen-rust 0.41.0",
]

[[package]]
name = "wit-component"
version = "0.201.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "421c0c848a0660a8c22e2fd217929a0191f14476b68962afd2af89fd22e39825"
dependencies = [
 "anyhow",
 "bitflags 2.9.0",
 "indexmap",
 "log",
 "serde",
 "serde_derive",
 "serde_json",
 "wasm-encoder 0.201.0",
 "wasm-metadata 0.201.0",
 "wasmparser 0.201.0",
 "wit-parser 0.201.0",
]

[[package]]
name = "wit-component"
version = "0.227.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "635c3adc595422cbf2341a17fb73a319669cc8d33deed3a48368a841df86b676"
dependencies = [
 "anyhow",
 "bitflags 2.9.0",
 "indexmap",
 "log",
 "serde",
 "serde_derive",
 "serde_json",
 "wasm-encoder 0.227.1",
 "wasm-metadata 0.227.1",
 "wasmparser 0.227.1",
 "wit-parser 0.227.1",
]

[[package]]
name = "wit-parser"
version = "0.201.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "196d3ecfc4b759a8573bf86a9b3f8996b304b3732e4c7de81655f875f6efdca6"
dependencies = [
 "anyhow",
 "id-arena",
 "indexmap",
 "log",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "unicode-xid",
 "wasmparser 0.201.0",
]

[[package]]
name = "wit-parser"
version = "0.221.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "896112579ed56b4a538b07a3d16e562d101ff6265c46b515ce0c701eef16b2ac"
dependencies = [
 "anyhow",
 "id-arena",
 "indexmap",
 "log",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "unicode-xid",
 "wasmparser 0.221.3",
]

[[package]]
name = "wit-parser"
version = "0.227.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddf445ed5157046e4baf56f9138c124a0824d4d1657e7204d71886ad8ce2fc11"
dependencies = [
 "anyhow",
 "id-arena",
 "indexmap",
 "log",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "unicode-xid",
 "wasmparser 0.227.1",
]

[[package]]
name = "witx"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e366f27a5cabcddb2706a78296a40b8fcc451e1a6aba2fc1d94b4a01bdaaef4b"
dependencies = [
 "anyhow",
 "log",
 "thiserror 1.0.69",
 "wast",
]

[[package]]
name = "workspace"
version = "0.1.0"
dependencies = [
 "any_vec",
 "anyhow",
 "async-recursion 1.1.1",
 "bincode",
 "call",
 "client",
 "clock",
 "collections",
 "component",
 "dap",
 "db",
 "fs",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "itertools 0.14.0",
 "language",
 "log",
 "menu",
 "node_runtime",
 "parking_lot",
 "postage",
 "project",
 "remote",
 "schemars",
 "serde",
 "serde_json",
 "session",
 "settings",
 "smallvec",
 "sqlez",
 "strum 0.27.1",
 "task",
 "telemetry",
 "tempfile",
 "theme",
 "ui",
 "util",
 "uuid",
 "windows 0.61.1",
 "workspace-hack",
 "zed_actions",
 "zlog",
]

[[package]]
name = "workspace-hack"
version = "0.1.0"
dependencies = [
 "aes",
 "ahash 0.8.11",
 "aho-corasick",
 "anstream",
 "arrayvec",
 "async-compression",
 "async-std",
 "async-tungstenite",
 "aws-config",
 "aws-credential-types",
 "aws-runtime",
 "aws-sigv4",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "base64 0.22.1",
 "base64ct",
 "bigdecimal",
 "bit-set 0.8.0",
 "bit-vec 0.8.0",
 "bitflags 2.9.0",
 "bstr",
 "bytemuck",
 "byteorder",
 "bytes 1.10.1",
 "cc",
 "chrono",
 "cipher",
 "clang-sys",
 "clap",
 "clap_builder",
 "codespan-reporting 0.12.0",
 "concurrent-queue",
 "core-foundation 0.9.4",
 "core-foundation-sys",
 "coreaudio-sys",
 "cranelift-codegen",
 "crc32fast",
 "crossbeam-epoch",
 "crossbeam-utils",
 "crypto-common",
 "deranged",
 "digest",
 "either",
 "euclid",
 "event-listener 5.4.0",
 "event-listener-strategy",
 "flate2",
 "flume",
 "foldhash",
 "form_urlencoded",
 "futures 0.3.31",
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
 "getrandom 0.2.15",
 "getrandom 0.3.2",
 "gimli",
 "half",
 "handlebars 4.5.0",
 "hashbrown 0.14.5",
 "hashbrown 0.15.3",
 "heck 0.4.1",
 "hmac",
 "hyper 0.14.32",
 "hyper-rustls 0.27.5",
 "idna",
 "indexmap",
 "inout",
 "itertools 0.12.1",
 "itertools 0.13.0",
 "jiff",
 "lazy_static",
 "libc",
 "libsqlite3-sys",
 "linux-raw-sys 0.4.15",
 "linux-raw-sys 0.9.4",
 "log",
 "lyon",
 "lyon_path",
 "md-5",
 "memchr",
 "miniz_oxide",
 "mio",
 "naga",
 "nix 0.29.0",
 "nom",
 "num-bigint",
 "num-bigint-dig",
 "num-integer",
 "num-iter",
 "num-rational",
 "num-traits",
 "objc2",
 "objc2-foundation",
 "objc2-metal",
 "object",
 "once_cell",
 "percent-encoding",
 "phf",
 "phf_shared",
 "prettyplease",
 "proc-macro2",
 "prost 0.9.0",
 "prost-types 0.9.0",
 "quote",
 "rand 0.8.5",
 "rand 0.9.1",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
 "regalloc2",
 "regex",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
 "ring",
 "rust_decimal",
 "rustc-hash 1.1.0",
 "rustix 0.38.44",
 "rustix 1.0.7",
 "rustls 0.23.26",
 "rustls-webpki 0.103.1",
 "scopeguard",
 "sea-orm",
 "sea-query-binder",
 "security-framework 3.2.0",
 "security-framework-sys",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "sha1",
 "simd-adler32",
 "smallvec",
 "spin",
 "sqlx",
 "sqlx-macros",
 "sqlx-macros-core",
 "sqlx-postgres",
 "sqlx-sqlite",
 "strum 0.26.3",
 "subtle",
 "syn 1.0.109",
 "syn 2.0.101",
 "sync_wrapper 1.0.2",
 "thiserror 2.0.12",
 "time",
 "time-macros",
 "tokio",
 "tokio-rustls 0.26.2",
 "tokio-socks",
 "tokio-stream",
 "tokio-util",
 "toml_datetime",
 "toml_edit",
 "tower 0.5.2",
 "tracing",
 "tracing-core",
 "tungstenite 0.26.2",
 "unicode-normalization",
 "unicode-properties",
 "url",
 "uuid",
 "wasmparser 0.221.3",
 "wasmtime",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "winapi",
 "windows-core 0.61.0",
 "windows-numerics",
 "windows-sys 0.48.0",
 "windows-sys 0.52.0",
 "windows-sys 0.59.0",
 "winnow",
 "zeroize",
 "zvariant",
]

[[package]]
name = "worktree"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clock",
 "collections",
 "fs",
 "futures 0.3.31",
 "fuzzy",
 "git",
 "git2",
 "gpui",
 "http_client",
 "ignore",
 "language",
 "log",
 "parking_lot",
 "paths",
 "postage",
 "pretty_assertions",
 "rand 0.8.5",
 "rpc",
 "schemars",
 "serde",
 "serde_json",
 "settings",
 "smallvec",
 "smol",
 "sum_tree",
 "text",
 "util",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "write16"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1890f4022759daae28ed4fe62859b1236caebfc61ede2f63ed4e695f3f6d936"

[[package]]
name = "writeable"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e9df38ee2d2c3c5948ea468a8406ff0db0b29ae1ffde1bcf20ef305bcc95c51"

[[package]]
name = "wyz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f360fc0b24296329c78fda852a1e9ae82de9cf7b27dae4b7f62f118f77b9ed"
dependencies = [
 "tap",
]

[[package]]
name = "x11"
version = "2.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "502da5464ccd04011667b11c435cb992822c2c0dbde1770c988480d312a0db2e"
dependencies = [
 "libc",
 "pkg-config",
]

[[package]]
name = "x11-clipboard"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "662d74b3d77e396b8e5beb00b9cad6a9eccf40b2ef68cc858784b14c41d535a3"
dependencies = [
 "libc",
 "x11rb",
]

[[package]]
name = "x11rb"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d91ffca73ee7f68ce055750bf9f6eca0780b8c85eff9bc046a3b0da41755e12"
dependencies = [
 "as-raw-xcb-connection",
 "gethostname",
 "libc",
 "rustix 0.38.44",
 "x11rb-protocol",
]

[[package]]
name = "x11rb-protocol"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec107c4503ea0b4a98ef47356329af139c0a4f7750e621cf2973cd3385ebcb3d"

[[package]]
name = "xattr"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d1526bbe5aaeb5eb06885f4d987bcdfa5e23187055de9b83fe00156a821fabc"
dependencies = [
 "libc",
]

[[package]]
name = "xcb"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1e2f212bb1a92cd8caac8051b829a6582ede155ccb60b5d5908b81b100952be"
dependencies = [
 "bitflags 1.3.2",
 "libc",
 "quick-xml 0.30.0",
 "x11",
]

[[package]]
name = "xcursor"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ef33da6b1660b4ddbfb3aef0ade110c8b8a781a3b6382fa5f2b5b040fd55f61"

[[package]]
name = "xdg-home"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec1cdab258fb55c0da61328dc52c8764709b249011b2cad0454c72f0bf10a1f6"
dependencies = [
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "xim"
version = "0.4.0"
source = "git+https://github.com/XDeme1/xim-rs?rev=d50d461764c2213655cd9cf65a0ea94c70d3c4fd#d50d461764c2213655cd9cf65a0ea94c70d3c4fd"
dependencies = [
 "ahash 0.8.11",
 "hashbrown 0.14.5",
 "log",
 "x11rb",
 "xim-ctext",
 "xim-parser",
]

[[package]]
name = "xim-ctext"
version = "0.3.0"
source = "git+https://github.com/XDeme1/xim-rs?rev=d50d461764c2213655cd9cf65a0ea94c70d3c4fd#d50d461764c2213655cd9cf65a0ea94c70d3c4fd"
dependencies = [
 "encoding_rs",
]

[[package]]
name = "xim-parser"
version = "0.2.1"
source = "git+https://github.com/XDeme1/xim-rs?rev=d50d461764c2213655cd9cf65a0ea94c70d3c4fd#d50d461764c2213655cd9cf65a0ea94c70d3c4fd"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "xkbcommon"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d66ca9352cbd4eecbbc40871d8a11b4ac8107cfc528a6e14d7c19c69d0e1ac9"
dependencies = [
 "as-raw-xcb-connection",
 "libc",
 "memmap2",
 "xkeysym",
]

[[package]]
name = "xkeysym"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9cc00251562a284751c9973bace760d86c0276c471b4be569fe6b068ee97a56"

[[package]]
name = "xml5ever"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bbb26405d8e919bc1547a5aa9abc95cbfa438f04844f5fdd9dc7596b748bf69"
dependencies = [
 "log",
 "mac",
 "markup5ever 0.12.1",
]

[[package]]
name = "xmlparser"
version = "0.13.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66fee0b777b0f5ac1c69bb06d361268faafa61cd4682ae064a171c16c433e9e4"

[[package]]
name = "xmlwriter"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec7a2a501ed189703dba8b08142f057e887dfc4b2cc4db2d343ac6376ba3e0b9"

[[package]]
name = "xtask"
version = "0.1.0"
dependencies = [
 "anyhow",
 "cargo_metadata",
 "cargo_toml",
 "clap",
 "workspace-hack",
]

[[package]]
name = "yaml-rust2"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8902160c4e6f2fb145dbe9d6760a75e3c9522d8bf796ed7047c85919ac7115f8"
dependencies = [
 "arraydeque",
 "encoding_rs",
 "hashlink 0.8.4",
]

[[package]]
name = "yansi"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfe53a6657fd280eaa890a3bc59152892ffa3e30101319d168b781ed6529b049"

[[package]]
name = "yazi"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e01738255b5a16e78bbb83e7fbba0a1e7dd506905cfc53f4622d89015a03fbb5"

[[package]]
name = "yeslogic-fontconfig-sys"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "503a066b4c037c440169d995b869046827dbc71263f6e8f3be6d77d4f3229dbd"
dependencies = [
 "dlib",
 "once_cell",
 "pkg-config",
]

[[package]]
name = "yoke"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "120e6aef9aa629e3d4f52dc8cc43a015c7724194c97dfaf45180d2daf2b77f40"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2380878cad4ac9aac1e2435f3eb4020e8374b5f13c296cb75b4620ff8e229154"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure",
]

[[package]]
name = "zbus"
version = "5.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59c333f648ea1b647bc95dc1d34807c8e25ed7a6feff3394034dc4776054b236"
dependencies = [
 "async-broadcast",
 "async-executor",
 "async-fs",
 "async-io",
 "async-lock",
 "async-process",
 "async-recursion 1.1.1",
 "async-task",
 "async-trait",
 "blocking",
 "enumflags2",
 "event-listener 5.4.0",
 "futures-core",
 "futures-lite 2.6.0",
 "hex",
 "nix 0.29.0",
 "ordered-stream",
 "serde",
 "serde_repr",
 "static_assertions",
 "tracing",
 "uds_windows",
 "windows-sys 0.59.0",
 "winnow",
 "xdg-home",
 "zbus_macros",
 "zbus_names",
 "zvariant",
]

[[package]]
name = "zbus_macros"
version = "5.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f325ad10eb0d0a3eb060203494c3b7ec3162a01a59db75d2deee100339709fc0"
dependencies = [
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "zbus_names",
 "zvariant",
 "zvariant_utils",
]

[[package]]
name = "zbus_names"
version = "4.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7be68e64bf6ce8db94f63e72f0c7eb9a60d733f7e0499e628dfab0f84d6bcb97"
dependencies = [
 "serde",
 "static_assertions",
 "winnow",
 "zvariant",
]

[[package]]
name = "zed"
version = "0.191.0"
dependencies = [
 "activity_indicator",
 "agent",
 "agent_settings",
 "anyhow",
 "ashpd",
 "askpass",
 "assets",
 "assistant_context_editor",
 "assistant_tool",
 "assistant_tools",
 "audio",
 "auto_update",
 "auto_update_ui",
 "backtrace",
 "breadcrumbs",
 "call",
 "channel",
 "chrono",
 "clap",
 "cli",
 "client",
 "collab_ui",
 "collections",
 "command_palette",
 "component",
 "copilot",
 "dap",
 "dap_adapters",
 "db",
 "debug_adapter_extension",
 "debugger_tools",
 "debugger_ui",
 "diagnostics",
 "editor",
 "env_logger 0.11.8",
 "extension",
 "extension_host",
 "extensions_ui",
 "feature_flags",
 "feedback",
 "file_finder",
 "fs",
 "futures 0.3.31",
 "git",
 "git_hosting_providers",
 "git_ui",
 "go_to_line",
 "gpui",
 "gpui_tokio",
 "http_client",
 "image_viewer",
 "indoc",
 "inline_completion_button",
 "inspector_ui",
 "install_cli",
 "jj_ui",
 "journal",
 "language",
 "language_extension",
 "language_model",
 "language_models",
 "language_selector",
 "language_tools",
 "languages",
 "libc",
 "log",
 "markdown",
 "markdown_preview",
 "menu",
 "migrator",
 "mimalloc",
 "nix 0.29.0",
 "node_runtime",
 "notifications",
 "outline",
 "outline_panel",
 "parking_lot",
 "paths",
 "picker",
 "profiling",
 "project",
 "project_panel",
 "project_symbols",
 "prompt_store",
 "proto",
 "recent_projects",
 "release_channel",
 "remote",
 "repl",
 "reqwest_client",
 "rope",
 "search",
 "serde",
 "serde_json",
 "session",
 "settings",
 "settings_ui",
 "shellexpand 2.1.2",
 "smol",
 "snippet_provider",
 "snippets_ui",
 "supermaven",
 "sysinfo",
 "tab_switcher",
 "task",
 "tasks_ui",
 "telemetry",
 "telemetry_events",
 "terminal_view",
 "theme",
 "theme_extension",
 "theme_selector",
 "time",
 "title_bar",
 "toolchain_selector",
 "tree-sitter-md",
 "tree-sitter-rust",
 "ui",
 "ui_input",
 "ui_prompt",
 "url",
 "urlencoding",
 "util",
 "uuid",
 "vim",
 "vim_mode_setting",
 "watch",
 "web_search",
 "web_search_providers",
 "welcome",
 "windows 0.61.1",
 "winresource",
 "workspace",
 "workspace-hack",
 "zed_actions",
 "zeta",
 "zlog",
 "zlog_settings",
]

[[package]]
name = "zed_actions"
version = "0.1.0"
dependencies = [
 "gpui",
 "schemars",
 "serde",
 "uuid",
 "workspace-hack",
]

[[package]]
name = "zed_emmet"
version = "0.0.3"
dependencies = [
 "zed_extension_api 0.1.0",
]

[[package]]
name = "zed_extension_api"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "594fd10dd0f2f853eb243e2425e7c95938cef49adb81d9602921d002c5e6d9d9"
dependencies = [
 "serde",
 "serde_json",
 "wit-bindgen 0.22.0",
]

[[package]]
name = "zed_extension_api"
version = "0.6.0"
dependencies = [
 "serde",
 "serde_json",
 "wit-bindgen 0.41.0",
]

[[package]]
name = "zed_glsl"
version = "0.1.0"
dependencies = [
 "zed_extension_api 0.1.0",
]

[[package]]
name = "zed_html"
version = "0.2.1"
dependencies = [
 "zed_extension_api 0.1.0",
]

[[package]]
name = "zed_llm_client"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de7d9523255f4e00ee3d0918e5407bd252d798a4a8e71f6d37f23317a1588203"
dependencies = [
 "anyhow",
 "serde",
 "serde_json",
 "strum 0.27.1",
 "uuid",
]

[[package]]
name = "zed_proto"
version = "0.2.2"
dependencies = [
 "zed_extension_api 0.1.0",
]

[[package]]
name = "zed_ruff"
version = "0.1.0"
dependencies = [
 "zed_extension_api 0.1.0",
]

[[package]]
name = "zed_snippets"
version = "0.0.5"
dependencies = [
 "serde_json",
 "zed_extension_api 0.1.0",
]

[[package]]
name = "zed_test_extension"
version = "0.1.0"
dependencies = [
 "zed_extension_api 0.6.0",
]

[[package]]
name = "zed_toml"
version = "0.1.4"
dependencies = [
 "zed_extension_api 0.1.0",
]

[[package]]
name = "zeno"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc0de2315dc13d00e5df3cd6b8d2124a6eaec6a2d4b6a1c5f37b7efad17fcc17"

[[package]]
name = "zerocopy"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b9b4fd18abc82b8136838da5d50bae7bdea537c574d8dc1a34ed098d6c166f0"
dependencies = [
 "zerocopy-derive 0.7.35",
]

[[package]]
name = "zerocopy"
version = "0.8.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2586fea28e186957ef732a5f8b3be2da217d65c5969d4b1e17f973ebbe876879"
dependencies = [
 "zerocopy-derive 0.8.24",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa4f8080344d4671fb4e831a13ad1e68092748387dfc4f55e356242fae12ce3e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zerocopy-derive"
version = "0.8.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a996a8f63c5c4448cd959ac1bab0aaa3306ccfd060472f85943ee0750f0169be"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zerofrom"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50cc42e0333e05660c3587f3bf9d0478688e15d870fab3346451ce7f8c9fbea5"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71e5d6e06ab090c67b5e44993ec16b72dcbaabc526db883a360057678b48502"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure",
]

[[package]]
name = "zeroize"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ced3678a2879b30306d323f4542626697a464a97c0a07c9aebf7ebca65cd4dde"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zeromq"
version = "0.5.0-pre"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1fe92954d37e77bed5e2775cb0fed7dba5f6bc4be6f7f76172a4eb371dc6a9b"
dependencies = [
 "async-dispatcher",
 "async-std",
 "async-trait",
 "asynchronous-codec",
 "bytes 1.10.1",
 "crossbeam-queue",
 "dashmap 5.5.3",
 "futures 0.3.31",
 "log",
 "num-traits",
 "once_cell",
 "parking_lot",
 "rand 0.8.5",
 "regex",
 "thiserror 1.0.69",
 "uuid",
]

[[package]]
name = "zerovec"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa2b893d79df23bfb12d5461018d408ea19dfafe76c2c7ef6d4eba614f8ff079"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6eafa6dfb17584ea3e2bd6e76e0cc15ad7af12b09abdd1ca55961bed9b1063c6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zeta"
version = "0.1.0"
dependencies = [
 "anyhow",
 "arrayvec",
 "call",
 "client",
 "clock",
 "collections",
 "command_palette_hooks",
 "ctor",
 "db",
 "editor",
 "feature_flags",
 "fs",
 "futures 0.3.31",
 "gpui",
 "http_client",
 "indoc",
 "inline_completion",
 "language",
 "language_model",
 "log",
 "menu",
 "migrator",
 "paths",
 "postage",
 "project",
 "proto",
 "regex",
 "release_channel",
 "reqwest_client",
 "rpc",
 "serde",
 "serde_json",
 "settings",
 "telemetry",
 "telemetry_events",
 "theme",
 "thiserror 2.0.12",
 "tree-sitter-go",
 "tree-sitter-rust",
 "ui",
 "unindent",
 "util",
 "uuid",
 "workspace",
 "workspace-hack",
 "worktree",
 "zed_actions",
 "zed_llm_client",
 "zlog",
]

[[package]]
name = "zip"
version = "0.6.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "760394e246e4c28189f19d488c058bf16f564016aefac5d32bb1f3b51d5e9261"
dependencies = [
 "aes",
 "byteorder",
 "bzip2",
 "constant_time_eq 0.1.5",
 "crc32fast",
 "crossbeam-utils",
 "flate2",
 "hmac",
 "pbkdf2 0.11.0",
 "sha1",
 "time",
 "zstd",
]

[[package]]
name = "zlib-rs"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "868b928d7949e09af2f6086dfc1e01936064cc7a819253bce650d4e2a2d63ba8"

[[package]]
name = "zlog"
version = "0.1.0"
dependencies = [
 "anyhow",
 "chrono",
 "log",
 "tempfile",
 "workspace-hack",
]

[[package]]
name = "zlog_settings"
version = "0.1.0"
dependencies = [
 "anyhow",
 "gpui",
 "schemars",
 "serde",
 "settings",
 "workspace-hack",
 "zlog",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.15+zstd.1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb81183ddd97d0c74cedf1d50d85c8d08c1b8b68ee863bdee9e706eedba1a237"
dependencies = [
 "cc",
 "pkg-config",
]

[[package]]
name = "zune-core"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f423a2c17029964870cfaabb1f13dfab7d092a62a29a89264f4d36990ca414a"

[[package]]
name = "zune-inflate"
version = "0.2.54"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73ab332fe2f6680068f3582b16a24f90ad7096d5d39b974d1c0aff0125116f02"
dependencies = [
 "simd-adler32",
]

[[package]]
name = "zune-jpeg"
version = "0.4.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99a5bab8d7dedf81405c4bb1f2b83ea057643d9cb28778cea9eecddeedd2e028"
dependencies = [
 "zune-core",
]

[[package]]
name = "zvariant"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2df9ee044893fcffbdc25de30546edef3e32341466811ca18421e3cd6c5a3ac"
dependencies = [
 "endi",
 "enumflags2",
 "serde",
 "static_assertions",
 "url",
 "winnow",
 "zvariant_derive",
 "zvariant_utils",
]

[[package]]
name = "zvariant_derive"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74170caa85b8b84cc4935f2d56a57c7a15ea6185ccdd7eadb57e6edd90f94b2f"
dependencies = [
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "zvariant_utils",
]

[[package]]
name = "zvariant_utils"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16edfee43e5d7b553b77872d99bc36afdda75c223ca7ad5e3fbecd82ca5fc34"
dependencies = [
 "proc-macro2",
 "quote",
 "serde",
 "static_assertions",
 "syn 2.0.101",
 "winnow",
]
