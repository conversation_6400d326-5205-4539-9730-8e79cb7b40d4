use std::sync::Arc;

use serde::{Deserialize, Serialize};
use strum::{EnumIter, EnumString, IntoStaticStr};

#[derive(
    Debug, PartialEq, Eq, Copy, Clone, EnumIter, EnumString, IntoStaticStr, Serialize, Deserialize,
)]
#[strum(serialize_all = "snake_case")]
pub enum IconName {
    Ai,
    AiAnthropic,
    AiBedrock,
    AiDeepSeek,
    AiEdit,
    AiGoogle,
    AiLmStudio,
    AiMistral,
    AiOllama,
    AiOpenAi,
    AiOpenRouter,
    AiZed,
    ArrowCircle,
    ArrowDown,
    ArrowDownFromLine,
    ArrowDownRight,
    ArrowLeft,
    ArrowRight,
    ArrowRightLeft,
    ArrowUp,
    ArrowUpFromLine,
    ArrowUpRight,
    ArrowUpRightAlt,
    AtSign,
    AudioOff,
    AudioOn,
    Backspace,
    Bell,
    BellDot,
    BellOff,
    BellRing,
    Binary,
    Blocks,
    Bolt,
    BoltFilled,
    Book,
    BookCopy,
    BookPlus,
    Brain,
    BugOff,
    CaseSensitive,
    Check,
    CheckDouble,
    ChevronDown,
    /// This chevron indicates a popover menu.
    ChevronDownSmall,
    ChevronLeft,
    ChevronRight,
    ChevronUp,
    ChevronUpDown,
    Circle,
    CircleOff,
    CircleHelp,
    Clipboard,
    Close,
    Cloud,
    Code,
    Cog,
    Command,
    Context,
    Control,
    Copilot,
    CopilotDisabled,
    CopilotError,
    CopilotInit,
    Copy,
    CountdownTimer,
    Crosshair,
    CursorIBeam,
    Dash,
    DatabaseZap,
    Debug,
    DebugBreakpoint,
    DebugContinue,
    DebugDisabledBreakpoint,
    DebugDisabledLogBreakpoint,
    DebugDetach,
    DebugIgnoreBreakpoints,
    DebugLogBreakpoint,
    DebugPause,
    DebugRestart,
    DebugStepBack,
    DebugStepInto,
    DebugStepOut,
    DebugStepOver,
    DebugStop,
    Delete,
    Diff,
    Disconnected,
    DocumentText,
    Download,
    Ellipsis,
    EllipsisVertical,
    Envelope,
    Eraser,
    Escape,
    Exit,
    ExpandDown,
    ExpandUp,
    ExpandVertical,
    ExternalLink,
    Eye,
    File,
    FileCode,
    FileCreate,
    FileDelete,
    FileDiff,
    FileDoc,
    FileGeneric,
    FileGit,
    FileLock,
    FileRust,
    FileSearch,
    FileText,
    FileToml,
    FileTree,
    Filter,
    Flame,
    Folder,
    FolderOpen,
    FolderX,
    Font,
    FontSize,
    FontWeight,
    ForwardArrow,
    Function,
    GenericClose,
    GenericMaximize,
    GenericMinimize,
    GenericRestore,
    GitBranch,
    GitBranchSmall,
    Github,
    Globe,
    Hammer,
    Hash,
    HistoryRerun,
    Image,
    Indicator,
    Info,
    InlayHint,
    Keyboard,
    Layout,
    Library,
    LightBulb,
    LineHeight,
    Link,
    ListCollapse,
    ListTodo,
    ListTree,
    ListX,
    LoadCircle,
    LockOutlined,
    MagnifyingGlass,
    MailOpen,
    Maximize,
    Menu,
    MenuAlt,
    MessageBubbles,
    Mic,
    MicMute,
    Microscope,
    Minimize,
    Option,
    PageDown,
    PageUp,
    PanelLeft,
    PanelRight,
    Pencil,
    Person,
    PersonCircle,
    PhoneIncoming,
    Pin,
    Play,
    PlayAlt,
    PlayBug,
    Plus,
    PocketKnife,
    Power,
    Public,
    PullRequest,
    Quote,
    RefreshTitle,
    Regex,
    ReplNeutral,
    Replace,
    ReplaceAll,
    ReplaceNext,
    ReplyArrowRight,
    Rerun,
    Return,
    Reveal,
    RotateCcw,
    RotateCw,
    Route,
    Save,
    Scissors,
    Screen,
    SearchCode,
    SearchSelection,
    SelectAll,
    Send,
    Server,
    Settings,
    SettingsAlt,
    Shift,
    Slash,
    SlashSquare,
    Sliders,
    SlidersVertical,
    Snip,
    Space,
    Sparkle,
    SparkleAlt,
    SparkleFilled,
    Spinner,
    Split,
    SquareDot,
    SquareMinus,
    SquarePlus,
    Star,
    StarFilled,
    Stop,
    StopFilled,
    Strikethrough,
    Supermaven,
    SupermavenDisabled,
    SupermavenError,
    SupermavenInit,
    SwatchBook,
    Tab,
    Terminal,
    TextSnippet,
    ThumbsDown,
    ThumbsUp,
    Trash,
    TrashAlt,
    Triangle,
    TriangleRight,
    Undo,
    Unpin,
    Update,
    UserCheck,
    UserGroup,
    UserRoundPen,
    Visible,
    Wand,
    Warning,
    WholeWord,
    X,
    XCircle,
    ZedAssistant,
    ZedAssistantFilled,
    ZedBurnMode,
    ZedBurnModeOn,
    ZedPredict,
    ZedPredictDisabled,
    ZedPredictDown,
    ZedPredictError,
    ZedPredictUp,
    ZedXCopilot,
}

impl IconName {
    /// Returns the path to this icon.
    pub fn path(&self) -> Arc<str> {
        let file_stem: &'static str = self.into();
        format!("icons/{file_stem}.svg").into()
    }
}
