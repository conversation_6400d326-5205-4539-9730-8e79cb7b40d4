[package]
name = "dap"
version = "0.1.0"
edition.workspace = true
publish.workspace = true
license = "GPL-3.0-or-later"

[lints]
workspace = true

[lib]
path = "src/dap.rs"
doctest = false

[features]
test-support = [
    "gpui/test-support",
    "util/test-support",
    "task/test-support",
    "async-pipe",
    "settings/test-support",
]

[dependencies]
anyhow.workspace = true
async-compression.workspace = true
async-pipe = { workspace = true, optional = true }
async-tar.workspace = true
async-trait.workspace = true
client.workspace = true
collections.workspace = true
dap-types.workspace = true
fs.workspace = true
futures.workspace = true
gpui.workspace = true
http_client.workspace = true
language.workspace = true
log.workspace = true
node_runtime.workspace = true
parking_lot.workspace = true
paths.workspace = true
proto.workspace = true
schemars.workspace = true
serde.workspace = true
serde_json.workspace = true
settings.workspace = true
smallvec.workspace = true
smol.workspace = true
task.workspace = true
telemetry.workspace = true
util.workspace = true
workspace-hack.workspace = true

[target.'cfg(not(windows))'.dependencies]
libc.workspace = true

[dev-dependencies]
async-pipe.workspace = true
gpui = { workspace = true, features = ["test-support"] }
settings = { workspace = true, features = ["test-support"] }
task = { workspace = true, features = ["test-support"] }
tree-sitter.workspace = true
tree-sitter-go.workspace = true
util = { workspace = true, features = ["test-support"] }
zlog.workspace = true
