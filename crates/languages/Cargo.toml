[package]
name = "languages"
version = "0.1.0"
edition.workspace = true
publish.workspace = true
license = "GPL-3.0-or-later"

[lints]
workspace = true

[features]
test-support = [
    "load-grammars"
]
load-grammars = [
    "tree-sitter",
    "tree-sitter-bash",
    "tree-sitter-c",
    "tree-sitter-cpp",
    "tree-sitter-css",
    "tree-sitter-diff",
    "tree-sitter-gitcommit",
    "tree-sitter-go",
    "tree-sitter-go-mod",
    "tree-sitter-gowork",
    "tree-sitter-jsdoc",
    "tree-sitter-json",
    "tree-sitter-md",
    "tree-sitter-python",
    "tree-sitter-regex",
    "tree-sitter-rust",
    "tree-sitter-typescript",
    "tree-sitter-yaml",
]

[dependencies]
anyhow.workspace = true
async-compression.workspace = true
async-tar.workspace = true
async-trait.workspace = true
chrono.workspace = true
collections.workspace = true
dap.workspace = true
futures.workspace = true
gpui.workspace = true
http_client.workspace = true
language.workspace = true
log.workspace = true
lsp.workspace = true
node_runtime.workspace = true
parking_lot.workspace = true
paths.workspace = true
pet-conda.workspace = true
pet-core.workspace = true
pet-fs.workspace = true
pet-poetry.workspace = true
pet-reporter.workspace = true
pet.workspace = true
project.workspace = true
regex.workspace = true
rope.workspace = true
rust-embed.workspace = true
schemars.workspace = true
serde.workspace = true
serde_json.workspace = true
serde_json_lenient.workspace = true
settings.workspace = true
smol.workspace = true
snippet_provider.workspace = true
task.workspace = true
toml.workspace = true
tree-sitter = { workspace = true, optional = true }
tree-sitter-bash = { workspace = true, optional = true }
tree-sitter-c = { workspace = true, optional = true }
tree-sitter-cpp = { workspace = true, optional = true }
tree-sitter-css = { workspace = true, optional = true }
tree-sitter-diff = { workspace = true, optional = true }
tree-sitter-gitcommit = { workspace = true, optional = true }
tree-sitter-go = { workspace = true, optional = true }
tree-sitter-go-mod = { workspace = true, optional = true }
tree-sitter-gowork = { workspace = true, optional = true }
tree-sitter-jsdoc = { workspace = true, optional = true }
tree-sitter-json = { workspace = true, optional = true }
tree-sitter-md = { workspace = true, optional = true }
tree-sitter-python = { workspace = true, optional = true }
tree-sitter-regex = { workspace = true, optional = true }
tree-sitter-rust = { workspace = true, optional = true }
tree-sitter-typescript = { workspace = true, optional = true }
tree-sitter-yaml = { workspace = true, optional = true }
util.workspace = true
workspace-hack.workspace = true

[dev-dependencies]
pretty_assertions.workspace = true
text.workspace = true
theme = { workspace = true, features = ["test-support"] }
tree-sitter-bash.workspace = true
tree-sitter-c.workspace = true
tree-sitter-css.workspace = true
tree-sitter-go.workspace = true
tree-sitter-python.workspace = true
tree-sitter-typescript.workspace = true
tree-sitter.workspace = true
unindent.workspace = true
workspace = { workspace = true, features = ["test-support"] }
