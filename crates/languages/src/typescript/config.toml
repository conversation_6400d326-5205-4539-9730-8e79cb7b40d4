name = "TypeScript"
grammar = "typescript"
path_suffixes = ["ts", "cts", "mts"]
first_line_pattern = '^#!.*\b(?:deno run|ts-node|bun|tsx|[/ ]node)\b'
line_comments = ["// "]
block_comment = ["/*", "*/"]
autoclose_before = ";:.,=}])>"
brackets = [
    { start = "{", end = "}", close = true, newline = true },
    { start = "[", end = "]", close = true, newline = true },
    { start = "(", end = ")", close = true, newline = true },
    { start = "<", end = ">", close = false, newline = true, not_in = ["string", "comment"] },
    { start = "\"", end = "\"", close = true, newline = false, not_in = ["string"] },
    { start = "'", end = "'", close = true, newline = false, not_in = ["string", "comment"] },
    { start = "`", end = "`", close = true, newline = false, not_in = ["string"] },
    { start = "/*", end = " */", close = true, newline = false, not_in = ["string", "comment"] },
]
word_characters = ["#", "$"]
prettier_parser_name = "typescript"
tab_size = 2
debuggers = ["JavaScript"]
documentation = { start = "/**", end = "*/", prefix = "* ", tab_size = 1 }

[overrides.string]
completion_query_characters = ["."]
prefer_label_for_snippet = true

[overrides.function_name_before_type_arguments]
prefer_label_for_snippet = true
